#!/bin/bash

echo "🚀 A股智能交易分析平台 - macOS专业版"
echo "=========================================="

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装，请先安装Python3"
    exit 1
fi

# 检查pip
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 未安装，请先安装pip3"
    exit 1
fi

echo "📦 检查并安装依赖包..."

# 创建虚拟环境（可选）
if [ ! -d "venv" ]; then
    echo "🔧 创建虚拟环境..."
    python3 -m venv venv
fi

# 激活虚拟环境
echo "🔧 激活虚拟环境..."
source venv/bin/activate

# 安装依赖
echo "📥 安装Python依赖包..."
pip install -r requirements.txt

echo ""
echo "🖥️  启动A股智能交易分析平台..."
echo ""
echo "💡 功能特色:"
echo "   • 实时股票数据监控"
echo "   • 智能选股系统"
echo "   • 专业技术分析图表"
echo "   • 自选股管理"
echo "   • 快捷键操作"
echo ""
echo "⌨️  快捷键说明:"
echo "   • F5: 刷新数据"
echo "   • Ctrl+A: 添加股票"
echo "   • Ctrl+S: 智能选股"
echo "   • Ctrl+1/2: 切换标签页"
echo "   • F11: 全屏模式"
echo "   • ESC: 退出全屏"
echo ""
echo "⚠️  注意事项:"
echo "   • 首次运行可能需要下载数据，请耐心等待"
echo "   • 建议在交易时间使用以获得最佳体验"
echo "   • 技术分析仅供参考，投资有风险"
echo ""

# 启动应用
python3 main.py
