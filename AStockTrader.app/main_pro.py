#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A股智能交易分析平台 - 专业版主程序
专为资深交易员设计的专业级股票分析工具
"""

import sys
import os
import json
import threading
import time
from datetime import datetime

# PyQt5 imports
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

# 导入专业组件
from data_provider_pro import real_data_provider
from trading_widgets_pro import (
    Level2QuoteWidget, QuickOrderWidget, PositionWidget, ProfessionalChartWidget
)

class ProfessionalMainWindow(QMainWindow):
    """专业交易主窗口"""
    
    def __init__(self):
        super().__init__()
        self.current_symbol = None
        self.watchlist = []
        self.init_ui()
        self.setup_data_updates()
        self.setup_professional_shortcuts()
        self.load_settings()
        
    def init_ui(self):
        """初始化专业交易界面"""
        self.setWindowTitle("A股智能交易分析平台 - 专业版 v2.0")
        self.setGeometry(50, 50, 1800, 1200)  # 更大的窗口
        
        # 设置专业深色主题
        self.setStyleSheet("""
            QMainWindow {
                background-color: #0d1117;
                color: #ffffff;
            }
            QWidget {
                background-color: #0d1117;
                color: #ffffff;
            }
            QSplitter::handle {
                background-color: #21262d;
                width: 2px;
                height: 2px;
            }
            QSplitter::handle:hover {
                background-color: #58a6ff;
            }
        """)
        
        # 创建专业菜单栏
        self.create_professional_menubar()
        
        # 创建专业工具栏
        self.create_professional_toolbar()
        
        # 创建专业状态栏
        self.create_professional_statusbar()
        
        # 创建专业布局
        self.create_professional_layout()
        
        # 设置窗口图标
        self.setWindowIcon(QIcon())
        
        # 窗口居中
        self.center_window()
    
    def create_professional_menubar(self):
        """创建专业菜单栏"""
        menubar = self.menuBar()
        menubar.setStyleSheet("""
            QMenuBar {
                background-color: #161b22;
                color: #ffffff;
                border-bottom: 1px solid #21262d;
                padding: 4px;
            }
            QMenuBar::item {
                background-color: transparent;
                padding: 6px 12px;
                border-radius: 4px;
            }
            QMenuBar::item:selected {
                background-color: #21262d;
            }
            QMenu {
                background-color: #161b22;
                color: #ffffff;
                border: 1px solid #21262d;
                border-radius: 6px;
            }
            QMenu::item {
                padding: 8px 20px;
                border-radius: 4px;
            }
            QMenu::item:selected {
                background-color: #21262d;
            }
        """)
        
        # 交易菜单
        trade_menu = menubar.addMenu('交易')
        
        buy_action = QAction('买入 (F1)', self)
        buy_action.setShortcut('F1')
        buy_action.triggered.connect(self.quick_buy)
        trade_menu.addAction(buy_action)
        
        sell_action = QAction('卖出 (F2)', self)
        sell_action.setShortcut('F2')
        sell_action.triggered.connect(self.quick_sell)
        trade_menu.addAction(sell_action)
        
        trade_menu.addSeparator()
        
        positions_action = QAction('持仓查询 (F3)', self)
        positions_action.setShortcut('F3')
        positions_action.triggered.connect(self.show_positions)
        trade_menu.addAction(positions_action)
        
        # 数据菜单
        data_menu = menubar.addMenu('数据')
        
        refresh_action = QAction('刷新数据 (F5)', self)
        refresh_action.setShortcut('F5')
        refresh_action.triggered.connect(self.refresh_all_data)
        data_menu.addAction(refresh_action)
        
        level2_action = QAction('Level-2行情 (F6)', self)
        level2_action.setShortcut('F6')
        level2_action.triggered.connect(self.toggle_level2)
        data_menu.addAction(level2_action)
        
        # 视图菜单
        view_menu = menubar.addMenu('视图')
        
        layout_action = QAction('切换布局 (F9)', self)
        layout_action.setShortcut('F9')
        layout_action.triggered.connect(self.toggle_layout)
        view_menu.addAction(layout_action)
        
        fullscreen_action = QAction('全屏 (F11)', self)
        fullscreen_action.setShortcut('F11')
        fullscreen_action.triggered.connect(self.toggle_fullscreen)
        view_menu.addAction(fullscreen_action)
    
    def create_professional_toolbar(self):
        """创建专业工具栏"""
        toolbar = self.addToolBar('专业工具栏')
        toolbar.setStyleSheet("""
            QToolBar {
                background-color: #161b22;
                border: none;
                spacing: 8px;
                padding: 8px;
            }
            QToolButton {
                background-color: #21262d;
                color: #ffffff;
                border: 1px solid #30363d;
                padding: 6px 12px;
                border-radius: 6px;
                font-size: 11px;
                font-weight: 500;
            }
            QToolButton:hover {
                background-color: #30363d;
                border-color: #58a6ff;
            }
            QToolButton:pressed {
                background-color: #1c2128;
            }
        """)
        
        # 快速交易按钮
        buy_btn = QAction('买入 (F1)', self)
        buy_btn.triggered.connect(self.quick_buy)
        toolbar.addAction(buy_btn)
        
        sell_btn = QAction('卖出 (F2)', self)
        sell_btn.triggered.connect(self.quick_sell)
        toolbar.addAction(sell_btn)
        
        toolbar.addSeparator()
        
        # 数据刷新
        refresh_btn = QAction('刷新 (F5)', self)
        refresh_btn.triggered.connect(self.refresh_all_data)
        toolbar.addAction(refresh_btn)
        
        toolbar.addSeparator()
        
        # 快速搜索
        self.quick_search = QLineEdit()
        self.quick_search.setPlaceholderText("快速搜索股票 (Ctrl+F)")
        self.quick_search.setStyleSheet("""
            QLineEdit {
                background-color: #21262d;
                color: #ffffff;
                border: 1px solid #30363d;
                padding: 6px 12px;
                border-radius: 6px;
                font-size: 11px;
                min-width: 200px;
            }
            QLineEdit:focus {
                border-color: #58a6ff;
                background-color: #0d1117;
            }
        """)
        self.quick_search.returnPressed.connect(self.quick_search_stock)
        toolbar.addWidget(self.quick_search)
        
        # 市场状态指示器
        toolbar.addSeparator()
        self.market_status_label = QLabel("市场状态: 开盘")
        self.market_status_label.setStyleSheet("color: #7ee787; font-weight: bold;")
        toolbar.addWidget(self.market_status_label)
    
    def create_professional_statusbar(self):
        """创建专业状态栏"""
        self.status_bar = self.statusBar()
        self.status_bar.setStyleSheet("""
            QStatusBar {
                background-color: #161b22;
                color: #8b949e;
                border-top: 1px solid #21262d;
                font-size: 10px;
            }
        """)
        
        # 连接状态
        self.connection_status = QLabel("数据连接: 正常")
        self.connection_status.setStyleSheet("color: #7ee787;")
        self.status_bar.addWidget(self.connection_status)
        
        # 更新时间
        self.last_update = QLabel("最后更新: --")
        self.status_bar.addPermanentWidget(self.last_update)
        
        # 延迟显示
        self.latency_label = QLabel("延迟: <50ms")
        self.latency_label.setStyleSheet("color: #7ee787;")
        self.status_bar.addPermanentWidget(self.latency_label)
        
        # 内存使用
        self.memory_label = QLabel("内存: 0MB")
        self.status_bar.addPermanentWidget(self.memory_label)
    
    def create_professional_layout(self):
        """创建专业交易布局"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主分割器（水平）
        main_splitter = QSplitter(Qt.Horizontal)
        main_splitter.setStyleSheet("""
            QSplitter::handle {
                background-color: #21262d;
                width: 3px;
            }
            QSplitter::handle:hover {
                background-color: #58a6ff;
            }
        """)
        
        # 左侧面板（自选股 + Level-2）
        left_panel = self.create_left_panel()
        
        # 中间面板（主图表）
        center_panel = self.create_center_panel()
        
        # 右侧面板（下单 + 持仓）
        right_panel = self.create_right_panel()
        
        # 设置分割比例
        main_splitter.addWidget(left_panel)
        main_splitter.addWidget(center_panel)
        main_splitter.addWidget(right_panel)
        main_splitter.setSizes([300, 1000, 300])  # 左:中:右 = 3:10:3
        
        # 主布局
        layout = QVBoxLayout()
        layout.setContentsMargins(2, 2, 2, 2)
        layout.addWidget(main_splitter)
        
        central_widget.setLayout(layout)
    
    def create_left_panel(self):
        """创建左侧面板"""
        left_widget = QWidget()
        left_layout = QVBoxLayout()
        left_layout.setContentsMargins(5, 5, 5, 5)
        left_layout.setSpacing(5)
        
        # 自选股列表
        self.watchlist_widget = self.create_professional_watchlist()
        
        # Level-2行情
        self.level2_widget = Level2QuoteWidget()
        
        # 垂直分割
        left_splitter = QSplitter(Qt.Vertical)
        left_splitter.addWidget(self.watchlist_widget)
        left_splitter.addWidget(self.level2_widget)
        left_splitter.setSizes([400, 300])
        
        left_layout.addWidget(left_splitter)
        left_widget.setLayout(left_layout)
        
        return left_widget
    
    def create_center_panel(self):
        """创建中间面板（主图表）"""
        # 专业K线图表
        self.chart_widget = ProfessionalChartWidget()
        return self.chart_widget
    
    def create_right_panel(self):
        """创建右侧面板"""
        right_widget = QWidget()
        right_layout = QVBoxLayout()
        right_layout.setContentsMargins(5, 5, 5, 5)
        right_layout.setSpacing(5)
        
        # 快速下单
        self.order_widget = QuickOrderWidget()
        self.order_widget.order_signal.connect(self.handle_order)
        
        # 持仓管理
        self.position_widget = PositionWidget()
        
        # 垂直分割
        right_splitter = QSplitter(Qt.Vertical)
        right_splitter.addWidget(self.order_widget)
        right_splitter.addWidget(self.position_widget)
        right_splitter.setSizes([400, 400])
        
        right_layout.addWidget(right_splitter)
        right_widget.setLayout(right_layout)
        
        return right_widget
    
    def create_professional_watchlist(self):
        """创建专业自选股列表"""
        widget = QWidget()
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)
        
        # 标题栏
        header_layout = QHBoxLayout()
        title_label = QLabel("自选股")
        title_label.setStyleSheet("color: #ffffff; font-size: 14px; font-weight: bold;")
        
        add_btn = QPushButton("+")
        add_btn.setFixedSize(24, 24)
        add_btn.setStyleSheet("""
            QPushButton {
                background-color: #238636;
                color: #ffffff;
                border: none;
                border-radius: 12px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2ea043;
            }
        """)
        add_btn.clicked.connect(self.add_stock_dialog)
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addWidget(add_btn)
        
        # 自选股表格
        self.watchlist_table = QTableWidget()
        self.watchlist_table.setColumnCount(5)
        self.watchlist_table.setHorizontalHeaderLabels(['代码', '名称', '现价', '涨跌%', '操作'])
        
        self.watchlist_table.setStyleSheet("""
            QTableWidget {
                background-color: #0d1117;
                color: #ffffff;
                gridline-color: #21262d;
                border: 1px solid #21262d;
                border-radius: 6px;
                font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
                font-size: 11px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #21262d;
            }
            QTableWidget::item:selected {
                background-color: #1f6feb;
            }
            QHeaderView::section {
                background-color: #161b22;
                color: #f0f6fc;
                padding: 8px;
                border: none;
                border-bottom: 1px solid #21262d;
                font-weight: 600;
                font-size: 10px;
            }
        """)
        
        # 设置表格属性
        self.watchlist_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.watchlist_table.setAlternatingRowColors(True)
        self.watchlist_table.verticalHeader().setVisible(False)
        self.watchlist_table.horizontalHeader().setStretchLastSection(True)
        
        # 双击选择股票
        self.watchlist_table.cellDoubleClicked.connect(self.on_stock_selected)
        
        layout.addLayout(header_layout)
        layout.addWidget(self.watchlist_table)
        widget.setLayout(layout)
        
        # 加载默认自选股
        self.load_default_watchlist()
        
        return widget
    
    def setup_data_updates(self):
        """设置实时数据更新"""
        # 启动实时数据提供者
        real_data_provider.start_realtime_updates()
        
        # 设置定时器更新界面
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_all_data)
        self.update_timer.start(1000)  # 1秒更新一次
    
    def setup_professional_shortcuts(self):
        """设置专业快捷键"""
        # 交易快捷键
        QShortcut(QKeySequence("F1"), self, self.quick_buy)
        QShortcut(QKeySequence("F2"), self, self.quick_sell)
        QShortcut(QKeySequence("F3"), self, self.show_positions)
        QShortcut(QKeySequence("F5"), self, self.refresh_all_data)
        QShortcut(QKeySequence("F6"), self, self.toggle_level2)
        QShortcut(QKeySequence("F9"), self, self.toggle_layout)
        QShortcut(QKeySequence("F11"), self, self.toggle_fullscreen)
        
        # 搜索快捷键
        QShortcut(QKeySequence("Ctrl+F"), self, self.focus_search)
        
        # 数字键快速选择股票
        for i in range(1, 10):
            QShortcut(QKeySequence(f"Ctrl+{i}"), self, 
                     lambda checked, idx=i-1: self.select_stock_by_index(idx))
        
        # ESC退出全屏
        QShortcut(QKeySequence("Escape"), self, self.exit_fullscreen)
    
    def load_default_watchlist(self):
        """加载默认自选股"""
        default_stocks = [
            {'code': '000001', 'name': '平安银行'},
            {'code': '600036', 'name': '招商银行'},
            {'code': '600519', 'name': '贵州茅台'},
            {'code': '000858', 'name': '五粮液'},
            {'code': '002415', 'name': '海康威视'}
        ]
        
        self.watchlist = default_stocks
        self.update_watchlist_display()
    
    def update_watchlist_display(self):
        """更新自选股显示"""
        self.watchlist_table.setRowCount(len(self.watchlist))
        
        for i, stock in enumerate(self.watchlist):
            # 获取实时数据
            quote = real_data_provider.get_realtime_quote(stock['code'])
            
            if quote:
                price = quote.get('price', 0)
                change_pct = quote.get('change_pct', 0)
                
                # 代码
                self.watchlist_table.setItem(i, 0, QTableWidgetItem(stock['code']))
                
                # 名称
                self.watchlist_table.setItem(i, 1, QTableWidgetItem(stock['name']))
                
                # 现价
                price_item = QTableWidgetItem(f"{price:.2f}")
                self.watchlist_table.setItem(i, 2, price_item)
                
                # 涨跌幅
                change_item = QTableWidgetItem(f"{change_pct:+.2f}%")
                color = "#ff6b6b" if change_pct > 0 else "#51cf66" if change_pct < 0 else "#ffffff"
                change_item.setForeground(QColor(color))
                self.watchlist_table.setItem(i, 3, change_item)
                
                # 操作按钮
                del_btn = QPushButton("×")
                del_btn.setFixedSize(20, 20)
                del_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #da3633;
                        color: #ffffff;
                        border: none;
                        border-radius: 10px;
                        font-size: 12px;
                        font-weight: bold;
                    }
                    QPushButton:hover {
                        background-color: #f85149;
                    }
                """)
                del_btn.clicked.connect(lambda checked, code=stock['code']: self.remove_stock(code))
                self.watchlist_table.setCellWidget(i, 4, del_btn)
    
    def center_window(self):
        """窗口居中"""
        screen = QApplication.primaryScreen().geometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)
    
    # 专业交易功能方法
    def quick_buy(self):
        """快速买入 (F1)"""
        if self.current_symbol:
            self.order_widget.direction_combo.setCurrentText('买入')
            self.order_widget.quantity_edit.setFocus()
    
    def quick_sell(self):
        """快速卖出 (F2)"""
        if self.current_symbol:
            self.order_widget.direction_combo.setCurrentText('卖出')
            self.order_widget.quantity_edit.setFocus()
    
    def show_positions(self):
        """显示持仓 (F3)"""
        # 切换到持仓面板
        pass
    
    def refresh_all_data(self):
        """刷新所有数据 (F5)"""
        self.connection_status.setText("数据连接: 刷新中...")
        self.connection_status.setStyleSheet("color: #f1e05a;")
        
        # 模拟刷新延迟
        QTimer.singleShot(1000, lambda: (
            self.connection_status.setText("数据连接: 正常"),
            self.connection_status.setStyleSheet("color: #7ee787;")
        ))
    
    def toggle_level2(self):
        """切换Level-2显示 (F6)"""
        if self.level2_widget.isVisible():
            self.level2_widget.hide()
        else:
            self.level2_widget.show()
    
    def toggle_layout(self):
        """切换布局 (F9)"""
        # 实现布局切换逻辑
        pass
    
    def toggle_fullscreen(self):
        """切换全屏 (F11)"""
        if self.isFullScreen():
            self.showNormal()
        else:
            self.showFullScreen()
    
    def exit_fullscreen(self):
        """退出全屏 (ESC)"""
        if self.isFullScreen():
            self.showNormal()
    
    def focus_search(self):
        """聚焦搜索框 (Ctrl+F)"""
        self.quick_search.setFocus()
        self.quick_search.selectAll()
    
    def select_stock_by_index(self, index):
        """通过索引选择股票 (Ctrl+1-9)"""
        if 0 <= index < len(self.watchlist):
            self.watchlist_table.selectRow(index)
            self.on_stock_selected(index, 0)
    
    def quick_search_stock(self):
        """快速搜索股票"""
        keyword = self.quick_search.text().strip()
        if keyword:
            results = real_data_provider.search_stocks(keyword)
            if results:
                self.show_search_results(results)
    
    def show_search_results(self, results):
        """显示搜索结果"""
        # 实现搜索结果显示
        pass
    
    def add_stock_dialog(self):
        """添加股票对话框"""
        # 实现添加股票对话框
        pass
    
    def remove_stock(self, code):
        """移除股票"""
        self.watchlist = [s for s in self.watchlist if s['code'] != code]
        self.update_watchlist_display()
    
    def on_stock_selected(self, row, column):
        """股票选择事件"""
        if row < len(self.watchlist):
            stock = self.watchlist[row]
            self.current_symbol = stock['code']
            
            # 更新图表
            quote = real_data_provider.get_realtime_quote(self.current_symbol)
            self.chart_widget.update_chart(self.current_symbol, quote)
            
            # 更新Level-2
            level2_data = real_data_provider.get_level2_data(self.current_symbol)
            self.level2_widget.update_level2_data(self.current_symbol, level2_data)
            
            # 更新下单面板
            self.order_widget.update_stock_info(self.current_symbol, quote)
    
    def handle_order(self, order_data):
        """处理下单"""
        print(f"收到下单请求: {order_data}")
        # 这里可以接入真实的交易接口
    
    def update_all_data(self):
        """更新所有数据"""
        # 更新自选股
        self.update_watchlist_display()
        
        # 更新当前股票的Level-2数据
        if self.current_symbol:
            level2_data = real_data_provider.get_level2_data(self.current_symbol)
            self.level2_widget.update_level2_data(self.current_symbol, level2_data)
        
        # 更新状态栏
        current_time = datetime.now().strftime("%H:%M:%S")
        self.last_update.setText(f"最后更新: {current_time}")
    
    def load_settings(self):
        """加载设置"""
        # 实现设置加载
        pass
    
    def save_settings(self):
        """保存设置"""
        # 实现设置保存
        pass
    
    def closeEvent(self, event):
        """关闭事件"""
        # 停止数据更新
        real_data_provider.stop_realtime_updates()
        
        # 保存设置
        self.save_settings()
        
        event.accept()

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用信息
    app.setApplicationName("A股智能交易分析平台-专业版")
    app.setApplicationVersion("2.0.0")
    app.setOrganizationName("ProfessionalTrader")
    
    # 创建主窗口
    window = ProfessionalMainWindow()
    window.show()
    
    # 运行应用
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
