#!/bin/bash

echo "🚀 A股智能交易分析平台 - 专业版 v2.0"
echo "=================================================="
echo "🎯 专为资深交易员设计的专业级交易工具"
echo ""

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装，请先安装Python3"
    exit 1
fi

echo "📊 专业版功能特色:"
echo "   ✅ 实时Level-2五档行情"
echo "   ✅ 毫秒级数据更新"
echo "   ✅ 快速下单系统"
echo "   ✅ 专业K线图表"
echo "   ✅ 持仓管理"
echo "   ✅ 风险控制"
echo "   ✅ 全键盘操作"
echo ""

echo "⌨️  专业快捷键:"
echo "   🔥 F1: 快速买入"
echo "   🔥 F2: 快速卖出"
echo "   📊 F3: 持仓查询"
echo "   🔄 F5: 刷新数据"
echo "   📈 F6: Level-2行情"
echo "   🖥️  F9: 切换布局"
echo "   🔍 Ctrl+F: 快速搜索"
echo "   🎯 Ctrl+1-9: 快选股票"
echo "   📺 F11: 全屏模式"
echo ""

echo "🔧 检查依赖包..."

# 安装依赖
pip3 install -r requirements.txt > /dev/null 2>&1

echo "🌐 连接数据源..."
echo "   📡 新浪财经: 实时行情"
echo "   📊 东方财富: Level-2数据"
echo "   🔍 雪球网: 股票搜索"
echo ""

echo "⚡ 启动专业交易平台..."
echo ""
echo "💡 专业提示:"
echo "   • 使用F1/F2进行快速交易"
echo "   • Level-2行情显示五档买卖盘"
echo "   • 支持限价、市价等多种委托类型"
echo "   • 实时风险监控和仓位管理"
echo "   • 所有功能支持键盘快捷操作"
echo ""
echo "⚠️  风险提示:"
echo "   • 当前为演示版本，请勿用于实盘交易"
echo "   • 投资有风险，入市需谨慎"
echo "   • 建议先在模拟环境中熟悉操作"
echo ""

# 启动专业版
python3 main_pro.py
