#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A股智能交易分析平台 - 简化实用版
专注于界面可用性和基本功能
"""

import sys
import json
import time
import random
from datetime import datetime
import numpy as np

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

class SimpleStockWidget(QWidget):
    """简化股票组件"""
    
    stock_selected = pyqtSignal(str, dict)
    
    def __init__(self):
        super().__init__()
        self.stocks = [
            {'code': '000001', 'name': '平安银行', 'price': 12.50, 'change': 0.25, 'change_pct': 2.04},
            {'code': '600036', 'name': '招商银行', 'price': 45.60, 'change': -0.80, 'change_pct': -1.72},
            {'code': '600519', 'name': '贵州茅台', 'price': 1680.00, 'change': 15.50, 'change_pct': 0.93},
            {'code': '000858', 'name': '五粮液', 'price': 158.20, 'change': -2.30, 'change_pct': -1.43},
            {'code': '002415', 'name': '海康威视', 'price': 32.80, 'change': 1.20, 'change_pct': 3.80}
        ]
        self.init_ui()
        self.start_price_update()
    
    def init_ui(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 标题
        title = QLabel("📈 自选股")
        title.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 18px;
                font-weight: bold;
                padding: 10px;
                background-color: #2d3748;
                border-radius: 8px;
            }
        """)
        
        # 股票列表
        self.stock_list = QListWidget()
        self.stock_list.setStyleSheet("""
            QListWidget {
                background-color: #1a202c;
                color: #ffffff;
                border: 2px solid #4a5568;
                border-radius: 8px;
                font-size: 14px;
                padding: 5px;
            }
            QListWidget::item {
                padding: 15px;
                border-bottom: 1px solid #4a5568;
                border-radius: 4px;
                margin: 2px;
            }
            QListWidget::item:hover {
                background-color: #2d3748;
            }
            QListWidget::item:selected {
                background-color: #3182ce;
            }
        """)
        
        self.update_stock_list()
        self.stock_list.itemClicked.connect(self.on_stock_clicked)
        
        # 添加按钮
        add_btn = QPushButton("➕ 添加股票")
        add_btn.setStyleSheet("""
            QPushButton {
                background-color: #38a169;
                color: #ffffff;
                border: none;
                padding: 12px 20px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2f855a;
            }
            QPushButton:pressed {
                background-color: #276749;
            }
        """)
        add_btn.clicked.connect(self.add_stock)
        
        layout.addWidget(title)
        layout.addWidget(self.stock_list)
        layout.addWidget(add_btn)
        
        self.setLayout(layout)
    
    def update_stock_list(self):
        """更新股票列表显示"""
        self.stock_list.clear()
        
        for stock in self.stocks:
            # 随机更新价格（模拟实时数据）
            price_change = random.uniform(-0.5, 0.5)
            stock['price'] += price_change
            stock['change'] += price_change
            stock['change_pct'] = (stock['change'] / (stock['price'] - stock['change'])) * 100
            
            # 创建显示文本
            color = "🔴" if stock['change'] > 0 else "🟢" if stock['change'] < 0 else "⚪"
            text = f"{color} {stock['code']} {stock['name']}\n"
            text += f"💰 ¥{stock['price']:.2f}  "
            text += f"📊 {stock['change']:+.2f} ({stock['change_pct']:+.2f}%)"
            
            item = QListWidgetItem(text)
            
            # 设置颜色
            if stock['change'] > 0:
                item.setForeground(QColor("#ff6b6b"))  # 红涨
            elif stock['change'] < 0:
                item.setForeground(QColor("#51cf66"))  # 绿跌
            else:
                item.setForeground(QColor("#ffffff"))
            
            item.setData(Qt.UserRole, stock)
            self.stock_list.addItem(item)
    
    def on_stock_clicked(self, item):
        """股票点击事件"""
        stock_data = item.data(Qt.UserRole)
        if stock_data:
            self.stock_selected.emit(stock_data['code'], stock_data)
    
    def add_stock(self):
        """添加股票"""
        dialog = QInputDialog(self)
        dialog.setWindowTitle("添加股票")
        dialog.setLabelText("请输入股票代码:")
        dialog.setStyleSheet("""
            QInputDialog {
                background-color: #2d3748;
                color: #ffffff;
            }
            QLineEdit {
                background-color: #1a202c;
                color: #ffffff;
                border: 2px solid #4a5568;
                padding: 8px;
                border-radius: 4px;
                font-size: 14px;
            }
        """)
        
        if dialog.exec_() == QDialog.Accepted:
            code = dialog.textValue().strip()
            if code:
                # 添加新股票（模拟数据）
                new_stock = {
                    'code': code,
                    'name': f'股票{code}',
                    'price': random.uniform(10, 100),
                    'change': random.uniform(-2, 2),
                    'change_pct': random.uniform(-5, 5)
                }
                self.stocks.append(new_stock)
                self.update_stock_list()
    
    def start_price_update(self):
        """启动价格更新定时器"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_stock_list)
        self.timer.start(3000)  # 3秒更新一次

class SimpleChartWidget(QWidget):
    """简化图表组件"""
    
    def __init__(self):
        super().__init__()
        self.current_stock = None
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 标题
        self.title_label = QLabel("📊 请选择股票查看图表")
        self.title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 18px;
                font-weight: bold;
                padding: 15px;
                background-color: #2d3748;
                border-radius: 8px;
                text-align: center;
            }
        """)
        
        # 股票信息面板
        self.info_panel = QWidget()
        self.info_panel.setStyleSheet("""
            QWidget {
                background-color: #1a202c;
                border: 2px solid #4a5568;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        
        info_layout = QGridLayout()
        
        # 创建信息标签
        self.price_label = QLabel("当前价格: --")
        self.change_label = QLabel("涨跌额: --")
        self.change_pct_label = QLabel("涨跌幅: --")
        self.volume_label = QLabel("成交量: --")
        
        labels = [self.price_label, self.change_label, self.change_pct_label, self.volume_label]
        for label in labels:
            label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 16px;
                    padding: 8px;
                    background-color: #2d3748;
                    border-radius: 4px;
                    margin: 2px;
                }
            """)
        
        info_layout.addWidget(self.price_label, 0, 0)
        info_layout.addWidget(self.change_label, 0, 1)
        info_layout.addWidget(self.change_pct_label, 1, 0)
        info_layout.addWidget(self.volume_label, 1, 1)
        
        self.info_panel.setLayout(info_layout)
        
        # 模拟图表区域
        self.chart_area = QLabel("📈 K线图表区域\n\n这里将显示专业K线图表\n包含技术指标和成交量")
        self.chart_area.setStyleSheet("""
            QLabel {
                background-color: #1a202c;
                color: #a0aec0;
                border: 2px solid #4a5568;
                border-radius: 8px;
                font-size: 16px;
                text-align: center;
                padding: 50px;
            }
        """)
        self.chart_area.setAlignment(Qt.AlignCenter)
        
        layout.addWidget(self.title_label)
        layout.addWidget(self.info_panel)
        layout.addWidget(self.chart_area, 1)  # 图表区域占更多空间
        
        self.setLayout(layout)
    
    def update_stock_info(self, code, stock_data):
        """更新股票信息"""
        self.current_stock = stock_data
        
        # 更新标题
        self.title_label.setText(f"📊 {code} - {stock_data['name']}")
        
        # 更新信息面板
        self.price_label.setText(f"💰 当前价格: ¥{stock_data['price']:.2f}")
        
        change = stock_data['change']
        change_pct = stock_data['change_pct']
        
        # 设置涨跌颜色
        if change > 0:
            color = "#ff6b6b"  # 红涨
            arrow = "📈"
        elif change < 0:
            color = "#51cf66"  # 绿跌
            arrow = "📉"
        else:
            color = "#ffffff"
            arrow = "➡️"
        
        self.change_label.setText(f"{arrow} 涨跌额: {change:+.2f}")
        self.change_label.setStyleSheet(f"""
            QLabel {{
                color: {color};
                font-size: 16px;
                font-weight: bold;
                padding: 8px;
                background-color: #2d3748;
                border-radius: 4px;
                margin: 2px;
            }}
        """)
        
        self.change_pct_label.setText(f"📊 涨跌幅: {change_pct:+.2f}%")
        self.change_pct_label.setStyleSheet(f"""
            QLabel {{
                color: {color};
                font-size: 16px;
                font-weight: bold;
                padding: 8px;
                background-color: #2d3748;
                border-radius: 4px;
                margin: 2px;
            }}
        """)
        
        # 模拟成交量
        volume = random.randint(1000000, 50000000)
        self.volume_label.setText(f"📦 成交量: {volume:,}")
        
        # 更新图表区域
        self.chart_area.setText(f"""📈 {code} - {stock_data['name']} K线图表

当前价格: ¥{stock_data['price']:.2f}
涨跌幅: {change_pct:+.2f}%

🔧 技术指标:
• MA5: {stock_data['price'] * 0.98:.2f}
• MA10: {stock_data['price'] * 0.95:.2f}
• RSI: {random.randint(30, 70)}
• MACD: {random.uniform(-1, 1):.3f}

💡 这里可以集成专业图表库显示真实K线图""")

class SimpleOrderWidget(QWidget):
    """简化下单组件"""
    
    def __init__(self):
        super().__init__()
        self.current_stock = None
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 标题
        title = QLabel("⚡ 快速交易")
        title.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 18px;
                font-weight: bold;
                padding: 10px;
                background-color: #2d3748;
                border-radius: 8px;
            }
        """)
        
        # 股票信息
        self.stock_info = QLabel("请先选择股票")
        self.stock_info.setStyleSheet("""
            QLabel {
                color: #a0aec0;
                font-size: 14px;
                padding: 10px;
                background-color: #1a202c;
                border: 2px solid #4a5568;
                border-radius: 6px;
            }
        """)
        
        # 交易表单
        form_widget = QWidget()
        form_widget.setStyleSheet("""
            QWidget {
                background-color: #1a202c;
                border: 2px solid #4a5568;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        
        form_layout = QFormLayout()
        
        # 交易方向
        self.direction_combo = QComboBox()
        self.direction_combo.addItems(['买入', '卖出'])
        
        # 价格
        self.price_spin = QDoubleSpinBox()
        self.price_spin.setRange(0.01, 9999.99)
        self.price_spin.setDecimals(2)
        self.price_spin.setValue(10.00)
        
        # 数量
        self.quantity_spin = QSpinBox()
        self.quantity_spin.setRange(100, 999999900)
        self.quantity_spin.setSingleStep(100)
        self.quantity_spin.setValue(1000)
        
        # 样式
        widgets = [self.direction_combo, self.price_spin, self.quantity_spin]
        for widget in widgets:
            widget.setStyleSheet("""
                QComboBox, QSpinBox, QDoubleSpinBox {
                    background-color: #2d3748;
                    color: #ffffff;
                    border: 2px solid #4a5568;
                    padding: 8px;
                    border-radius: 4px;
                    font-size: 14px;
                }
                QComboBox::drop-down {
                    border: none;
                }
                QComboBox::down-arrow {
                    image: none;
                    border-left: 5px solid transparent;
                    border-right: 5px solid transparent;
                    border-top: 5px solid #ffffff;
                }
            """)
        
        form_layout.addRow("交易方向:", self.direction_combo)
        form_layout.addRow("委托价格:", self.price_spin)
        form_layout.addRow("委托数量:", self.quantity_spin)
        
        form_widget.setLayout(form_layout)
        
        # 预计金额
        self.amount_label = QLabel("预计金额: ¥0.00")
        self.amount_label.setStyleSheet("""
            QLabel {
                color: #ffd700;
                font-size: 16px;
                font-weight: bold;
                padding: 10px;
                background-color: #2d3748;
                border-radius: 6px;
                text-align: center;
            }
        """)
        
        # 连接信号
        self.price_spin.valueChanged.connect(self.update_amount)
        self.quantity_spin.valueChanged.connect(self.update_amount)
        
        # 交易按钮
        button_layout = QHBoxLayout()
        
        self.buy_btn = QPushButton("🔴 买入")
        self.buy_btn.setStyleSheet("""
            QPushButton {
                background-color: #e53e3e;
                color: #ffffff;
                border: none;
                padding: 15px 25px;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c53030;
            }
            QPushButton:pressed {
                background-color: #9c2626;
            }
        """)
        self.buy_btn.clicked.connect(self.buy_stock)
        
        self.sell_btn = QPushButton("🟢 卖出")
        self.sell_btn.setStyleSheet("""
            QPushButton {
                background-color: #38a169;
                color: #ffffff;
                border: none;
                padding: 15px 25px;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2f855a;
            }
            QPushButton:pressed {
                background-color: #276749;
            }
        """)
        self.sell_btn.clicked.connect(self.sell_stock)
        
        button_layout.addWidget(self.buy_btn)
        button_layout.addWidget(self.sell_btn)
        
        layout.addWidget(title)
        layout.addWidget(self.stock_info)
        layout.addWidget(form_widget)
        layout.addWidget(self.amount_label)
        layout.addLayout(button_layout)
        layout.addStretch()
        
        self.setLayout(layout)
        self.update_amount()
    
    def update_stock_info(self, code, stock_data):
        """更新股票信息"""
        self.current_stock = stock_data
        self.stock_info.setText(f"📈 {code} - {stock_data['name']}\n💰 当前价格: ¥{stock_data['price']:.2f}")
        self.price_spin.setValue(stock_data['price'])
        self.update_amount()
    
    def update_amount(self):
        """更新预计金额"""
        price = self.price_spin.value()
        quantity = self.quantity_spin.value()
        amount = price * quantity
        self.amount_label.setText(f"💰 预计金额: ¥{amount:,.2f}")
    
    def buy_stock(self):
        """买入股票"""
        if not self.current_stock:
            QMessageBox.warning(self, "提示", "请先选择股票")
            return
        
        self.execute_trade("买入")
    
    def sell_stock(self):
        """卖出股票"""
        if not self.current_stock:
            QMessageBox.warning(self, "提示", "请先选择股票")
            return
        
        self.execute_trade("卖出")
    
    def execute_trade(self, direction):
        """执行交易"""
        price = self.price_spin.value()
        quantity = self.quantity_spin.value()
        amount = price * quantity
        
        msg = f"确认{direction}？\n\n"
        msg += f"股票: {self.current_stock['code']} - {self.current_stock['name']}\n"
        msg += f"价格: ¥{price:.2f}\n"
        msg += f"数量: {quantity:,}股\n"
        msg += f"金额: ¥{amount:,.2f}"
        
        reply = QMessageBox.question(self, "确认交易", msg, 
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            QMessageBox.information(self, "交易成功", 
                                  f"{direction}委托已提交！\n"
                                  f"委托编号: T{int(time.time())}")

class SimpleMainWindow(QMainWindow):
    """简化主窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        self.setWindowTitle("A股智能交易分析平台 - 简化版")
        self.setGeometry(100, 100, 1400, 900)
        
        # 设置深色主题
        self.setStyleSheet("""
            QMainWindow {
                background-color: #1a202c;
                color: #ffffff;
            }
        """)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout()
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(10)
        
        # 左侧：自选股
        self.stock_widget = SimpleStockWidget()
        self.stock_widget.setFixedWidth(350)
        
        # 中间：图表
        self.chart_widget = SimpleChartWidget()
        
        # 右侧：交易
        self.order_widget = SimpleOrderWidget()
        self.order_widget.setFixedWidth(300)
        
        # 连接信号
        self.stock_widget.stock_selected.connect(self.on_stock_selected)
        
        main_layout.addWidget(self.stock_widget)
        main_layout.addWidget(self.chart_widget, 1)  # 图表占更多空间
        main_layout.addWidget(self.order_widget)
        
        central_widget.setLayout(main_layout)
        
        # 状态栏
        self.statusBar().showMessage("✅ 系统就绪 - 点击股票开始交易")
        self.statusBar().setStyleSheet("""
            QStatusBar {
                background-color: #2d3748;
                color: #ffffff;
                border-top: 1px solid #4a5568;
                font-size: 12px;
            }
        """)
    
    def on_stock_selected(self, code, stock_data):
        """股票选择事件"""
        self.chart_widget.update_stock_info(code, stock_data)
        self.order_widget.update_stock_info(code, stock_data)
        self.statusBar().showMessage(f"📈 已选择: {code} - {stock_data['name']}")

def main():
    app = QApplication(sys.argv)
    app.setApplicationName("A股智能交易分析平台-简化版")
    
    window = SimpleMainWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
