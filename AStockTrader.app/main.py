#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A股智能交易分析平台 - macOS原生应用
专为交易员设计的专业级股票分析工具
"""

import sys
import os
import json
import threading
import time
from datetime import datetime, timedelta
import requests
import pandas as pd
import numpy as np

# PyQt5 imports
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QGridLayout, QTableWidget, QTableWidgetItem, QLabel, QPushButton,
    QLineEdit, QComboBox, QTabWidget, QSplitter, QTextEdit, QProgressBar,
    QGroupBox, QCheckBox, QSpinBox, QDoubleSpinBox, QSlider, QListWidget,
    QTreeWidget, QTreeWidgetItem, QHeaderView, QAbstractItemView,
    QMessageBox, QDialog, QDialogButtonBox, QFormLayout, QFrame,
    QScrollArea, QToolBar, QStatusBar, QMenuBar, QMenu, QSystemTrayIcon
)
from PyQt5.QtCore import (
    Qt, QTimer, QThread, pyqtSignal, QSettings, QSize, QRect,
    QPropertyAnimation, QEasingCurve, QParallelAnimationGroup
)
from PyQt5.QtGui import (
    QFont, QColor, QPalette, QPixmap, QIcon, QPainter, QBrush,
    QLinearGradient, QKeySequence
)
from PyQt5.QtWidgets import QAction, QShortcut

# 尝试导入图表库
try:
    import pyqtgraph as pg
    CHARTS_AVAILABLE = True
except ImportError:
    CHARTS_AVAILABLE = False
    print("警告: pyqtgraph未安装，图表功能将被禁用")

class StockDataProvider:
    """实时股票数据提供者"""

    def __init__(self):
        self.cache = {}
        self.cache_timeout = 30  # 30秒缓存
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
        })

    def get_market_indices(self):
        """获取市场指数"""
        try:
            # 模拟实时数据，实际应用中接入真实API
            current_time = int(time.time()) // 30
            np.random.seed(current_time)

            indices = {
                'sh000001': {
                    'name': '上证指数',
                    'code': 'sh000001',
                    'price': 3000 + np.random.normal(0, 50),
                    'change': np.random.normal(0, 20),
                    'change_pct': np.random.normal(0, 1.5),
                    'volume': np.random.randint(100000000, 500000000),
                    'turnover': np.random.randint(200000000000, 800000000000)
                },
                'sz399001': {
                    'name': '深证成指',
                    'code': 'sz399001',
                    'price': 10000 + np.random.normal(0, 200),
                    'change': np.random.normal(0, 50),
                    'change_pct': np.random.normal(0, 1.2),
                    'volume': np.random.randint(80000000, 400000000),
                    'turnover': np.random.randint(150000000000, 600000000000)
                },
                'sz399006': {
                    'name': '创业板指',
                    'code': 'sz399006',
                    'price': 2000 + np.random.normal(0, 100),
                    'change': np.random.normal(0, 30),
                    'change_pct': np.random.normal(0, 2.0),
                    'volume': np.random.randint(50000000, 300000000),
                    'turnover': np.random.randint(100000000000, 400000000000)
                }
            }

            # 格式化数据
            for key in indices:
                indices[key]['price'] = round(indices[key]['price'], 2)
                indices[key]['change'] = round(indices[key]['change'], 2)
                indices[key]['change_pct'] = round(indices[key]['change_pct'], 2)

            return indices

        except Exception as e:
            print(f"获取市场指数失败: {e}")
            return {}

    def get_stock_info(self, symbol):
        """获取股票实时信息"""
        try:
            # 检查缓存
            cache_key = f"stock_{symbol}"
            current_time = time.time()

            if cache_key in self.cache:
                data, timestamp = self.cache[cache_key]
                if current_time - timestamp < self.cache_timeout:
                    return data

            # 生成模拟实时数据
            np.random.seed(hash(symbol + str(int(current_time // 30))) % 2**32)

            # 股票名称映射
            stock_names = {
                '000001': '平安银行', '000002': '万科A', '600036': '招商银行',
                '600519': '贵州茅台', '000858': '五粮液', '002415': '海康威视',
                '000063': '中兴通讯', '600887': '伊利股份', '002304': '洋河股份',
                '300059': '东方财富', '000166': '申万宏源', '600000': '浦发银行'
            }

            base_price = 10 + (hash(symbol) % 100)
            price_change_pct = np.random.normal(0, 2.5)
            current_price = base_price * (1 + price_change_pct / 100)

            stock_data = {
                'symbol': symbol,
                'name': stock_names.get(symbol, f'股票{symbol}'),
                'price': round(current_price, 2),
                'change': round(current_price - base_price, 2),
                'change_pct': round(price_change_pct, 2),
                'volume': int(np.random.randint(1000000, 100000000)),
                'turnover': round(current_price * np.random.randint(1000000, 100000000), 2),
                'high': round(current_price * (1 + abs(np.random.normal(0, 0.02))), 2),
                'low': round(current_price * (1 - abs(np.random.normal(0, 0.02))), 2),
                'open': round(current_price * (1 + np.random.normal(0, 0.01)), 2),
                'prev_close': round(base_price, 2),
                'market_cap': round(current_price * np.random.randint(100000000, 10000000000), 2),
                'pe_ratio': round(np.random.uniform(8, 50), 2),
                'pb_ratio': round(np.random.uniform(0.5, 5), 2),
                'timestamp': current_time
            }

            # 缓存数据
            self.cache[cache_key] = (stock_data, current_time)
            return stock_data

        except Exception as e:
            print(f"获取股票信息失败: {e}")
            return None

    def search_stocks(self, keyword):
        """搜索股票"""
        # 模拟股票搜索
        all_stocks = [
            ('000001', '平安银行'), ('000002', '万科A'), ('600036', '招商银行'),
            ('600519', '贵州茅台'), ('000858', '五粮液'), ('002415', '海康威视'),
            ('000063', '中兴通讯'), ('600887', '伊利股份'), ('002304', '洋河股份'),
            ('300059', '东方财富'), ('000166', '申万宏源'), ('600000', '浦发银行'),
            ('000725', '桐昆股份'), ('002142', '宁波银行'), ('600276', '恒瑞医药')
        ]

        results = []
        keyword = keyword.lower()

        for code, name in all_stocks:
            if keyword in code or keyword in name.lower():
                results.append({'code': code, 'name': name})

        return results[:10]  # 返回前10个结果

class TechnicalAnalyzer:
    """技术分析器"""

    @staticmethod
    def calculate_ma(prices, period):
        """计算移动平均线"""
        if len(prices) < period:
            return [np.nan] * len(prices)

        ma = []
        for i in range(len(prices)):
            if i < period - 1:
                ma.append(np.nan)
            else:
                ma.append(np.mean(prices[i-period+1:i+1]))
        return ma

    @staticmethod
    def calculate_rsi(prices, period=14):
        """计算RSI"""
        if len(prices) < period + 1:
            return [50] * len(prices)

        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)

        avg_gains = pd.Series(gains).rolling(window=period).mean()
        avg_losses = pd.Series(losses).rolling(window=period).mean()

        rs = avg_gains / (avg_losses + 1e-10)
        rsi = 100 - (100 / (1 + rs))

        return [50] + rsi.fillna(50).tolist()

    @staticmethod
    def calculate_macd(prices, fast=12, slow=26, signal=9):
        """计算MACD"""
        if len(prices) < slow:
            return [0] * len(prices), [0] * len(prices), [0] * len(prices)

        prices_series = pd.Series(prices)
        ema_fast = prices_series.ewm(span=fast).mean()
        ema_slow = prices_series.ewm(span=slow).mean()

        macd = ema_fast - ema_slow
        signal_line = macd.ewm(span=signal).mean()
        histogram = macd - signal_line

        return macd.fillna(0).tolist(), signal_line.fillna(0).tolist(), histogram.fillna(0).tolist()

class StockDataThread(QThread):
    """股票数据更新线程"""

    data_updated = pyqtSignal(dict)

    def __init__(self, symbols, provider):
        super().__init__()
        self.symbols = symbols
        self.provider = provider
        self.running = True

    def run(self):
        while self.running:
            try:
                data = {}

                # 获取市场指数
                indices = self.provider.get_market_indices()
                data['indices'] = indices

                # 获取股票数据
                stocks = {}
                for symbol in self.symbols:
                    stock_data = self.provider.get_stock_info(symbol)
                    if stock_data:
                        stocks[symbol] = stock_data

                data['stocks'] = stocks

                self.data_updated.emit(data)

            except Exception as e:
                print(f"数据更新线程错误: {e}")

            time.sleep(5)  # 5秒更新一次

    def stop(self):
        self.running = False
        self.quit()
        self.wait()

class MarketOverviewWidget(QWidget):
    """市场概览组件"""

    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        layout = QHBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(20)

        # 创建指数显示卡片
        self.index_cards = {}
        indices = ['sh000001', 'sz399001', 'sz399006']
        names = ['上证指数', '深证成指', '创业板指']

        for index, name in zip(indices, names):
            card = self.create_index_card(name)
            self.index_cards[index] = card
            layout.addWidget(card)

        layout.addStretch()
        self.setLayout(layout)

    def create_index_card(self, name):
        """创建指数卡片"""
        card = QFrame()
        card.setFrameStyle(QFrame.Box)
        card.setStyleSheet("""
            QFrame {
                background-color: #2b2b2b;
                border: 1px solid #404040;
                border-radius: 8px;
                padding: 10px;
            }
        """)
        card.setFixedSize(200, 100)

        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)

        # 指数名称
        name_label = QLabel(name)
        name_label.setStyleSheet("color: #ffffff; font-size: 14px; font-weight: bold;")
        name_label.setAlignment(Qt.AlignCenter)

        # 指数值
        value_label = QLabel("0.00")
        value_label.setStyleSheet("color: #ffffff; font-size: 18px; font-weight: bold;")
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setObjectName("value")

        # 涨跌幅
        change_label = QLabel("0.00%")
        change_label.setStyleSheet("color: #00ff00; font-size: 12px;")
        change_label.setAlignment(Qt.AlignCenter)
        change_label.setObjectName("change")

        layout.addWidget(name_label)
        layout.addWidget(value_label)
        layout.addWidget(change_label)

        card.setLayout(layout)
        return card

    def update_indices(self, indices_data):
        """更新指数数据"""
        for index_code, card in self.index_cards.items():
            if index_code in indices_data:
                data = indices_data[index_code]

                # 更新数值
                value_label = card.findChild(QLabel, "value")
                change_label = card.findChild(QLabel, "change")

                if value_label:
                    value_label.setText(f"{data['price']:.2f}")

                if change_label:
                    change_pct = data['change_pct']
                    change_text = f"{change_pct:+.2f}%"
                    change_label.setText(change_text)

                    # 设置颜色
                    if change_pct > 0:
                        change_label.setStyleSheet("color: #ff4444; font-size: 12px;")  # 中国股市红涨
                    elif change_pct < 0:
                        change_label.setStyleSheet("color: #00ff00; font-size: 12px;")  # 中国股市绿跌
                    else:
                        change_label.setStyleSheet("color: #ffffff; font-size: 12px;")

class StockListWidget(QWidget):
    """股票列表组件"""

    stock_selected = pyqtSignal(str)  # 股票选择信号

    def __init__(self):
        super().__init__()
        self.watchlist = []
        self.init_ui()
        self.load_watchlist()

    def init_ui(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(5, 5, 5, 5)

        # 标题和控制按钮
        header_layout = QHBoxLayout()

        title_label = QLabel("自选股")
        title_label.setStyleSheet("color: #ffffff; font-size: 16px; font-weight: bold;")

        add_button = QPushButton("添加")
        add_button.setStyleSheet("""
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 5px 15px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
        """)
        add_button.clicked.connect(self.add_stock_dialog)

        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addWidget(add_button)

        # 股票表格
        self.table = QTableWidget()
        self.table.setColumnCount(6)
        self.table.setHorizontalHeaderLabels(['代码', '名称', '现价', '涨跌', '涨跌%', '操作'])

        # 设置表格样式
        self.table.setStyleSheet("""
            QTableWidget {
                background-color: #1e1e1e;
                color: #ffffff;
                gridline-color: #404040;
                border: 1px solid #404040;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #404040;
            }
            QTableWidget::item:selected {
                background-color: #0078d4;
            }
            QHeaderView::section {
                background-color: #2b2b2b;
                color: #ffffff;
                padding: 8px;
                border: 1px solid #404040;
                font-weight: bold;
            }
        """)

        # 设置表格属性
        self.table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.table.setAlternatingRowColors(True)
        self.table.horizontalHeader().setStretchLastSection(True)
        self.table.verticalHeader().setVisible(False)

        # 双击选择股票
        self.table.cellDoubleClicked.connect(self.on_stock_selected)

        layout.addLayout(header_layout)
        layout.addWidget(self.table)

        self.setLayout(layout)

    def add_stock_dialog(self):
        """添加股票对话框"""
        dialog = QDialog(self)
        dialog.setWindowTitle("添加股票")
        dialog.setModal(True)
        dialog.resize(300, 150)

        layout = QFormLayout()

        code_edit = QLineEdit()
        code_edit.setPlaceholderText("输入股票代码，如：000001")

        name_edit = QLineEdit()
        name_edit.setPlaceholderText("输入股票名称，如：平安银行")

        layout.addRow("股票代码:", code_edit)
        layout.addRow("股票名称:", name_edit)

        buttons = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        buttons.accepted.connect(dialog.accept)
        buttons.rejected.connect(dialog.reject)

        layout.addWidget(buttons)
        dialog.setLayout(layout)

        if dialog.exec_() == QDialog.Accepted:
            code = code_edit.text().strip()
            name = name_edit.text().strip()

            if code and name:
                self.add_stock(code, name)

    def add_stock(self, code, name):
        """添加股票到自选"""
        if code not in [stock['code'] for stock in self.watchlist]:
            self.watchlist.append({'code': code, 'name': name})
            self.save_watchlist()
            self.update_table()

    def remove_stock(self, code):
        """移除股票"""
        self.watchlist = [stock for stock in self.watchlist if stock['code'] != code]
        self.save_watchlist()
        self.update_table()

    def update_stocks_data(self, stocks_data):
        """更新股票数据"""
        for i in range(self.table.rowCount()):
            code_item = self.table.item(i, 0)
            if code_item:
                code = code_item.text()
                if code in stocks_data:
                    data = stocks_data[code]

                    # 更新价格
                    price_item = self.table.item(i, 2)
                    if price_item:
                        price_item.setText(f"{data['price']:.2f}")

                    # 更新涨跌额
                    change_item = self.table.item(i, 3)
                    if change_item:
                        change = data['change']
                        change_item.setText(f"{change:+.2f}")

                        # 设置颜色
                        if change > 0:
                            change_item.setForeground(QColor("#ff4444"))  # 红涨
                        elif change < 0:
                            change_item.setForeground(QColor("#00ff00"))  # 绿跌
                        else:
                            change_item.setForeground(QColor("#ffffff"))

                    # 更新涨跌幅
                    change_pct_item = self.table.item(i, 4)
                    if change_pct_item:
                        change_pct = data['change_pct']
                        change_pct_item.setText(f"{change_pct:+.2f}%")

                        # 设置颜色
                        if change_pct > 0:
                            change_pct_item.setForeground(QColor("#ff4444"))  # 红涨
                        elif change_pct < 0:
                            change_pct_item.setForeground(QColor("#00ff00"))  # 绿跌
                        else:
                            change_pct_item.setForeground(QColor("#ffffff"))

    def update_table(self):
        """更新表格显示"""
        self.table.setRowCount(len(self.watchlist))

        for i, stock in enumerate(self.watchlist):
            # 代码
            self.table.setItem(i, 0, QTableWidgetItem(stock['code']))

            # 名称
            self.table.setItem(i, 1, QTableWidgetItem(stock['name']))

            # 现价（初始为空，等待数据更新）
            self.table.setItem(i, 2, QTableWidgetItem("--"))

            # 涨跌
            self.table.setItem(i, 3, QTableWidgetItem("--"))

            # 涨跌%
            self.table.setItem(i, 4, QTableWidgetItem("--"))

            # 操作按钮
            remove_btn = QPushButton("删除")
            remove_btn.setStyleSheet("""
                QPushButton {
                    background-color: #d13438;
                    color: white;
                    border: none;
                    padding: 4px 8px;
                    border-radius: 3px;
                    font-size: 11px;
                }
                QPushButton:hover {
                    background-color: #b71c1c;
                }
            """)
            remove_btn.clicked.connect(lambda checked, code=stock['code']: self.remove_stock(code))
            self.table.setCellWidget(i, 5, remove_btn)

    def on_stock_selected(self, row, column):
        """股票选择事件"""
        code_item = self.table.item(row, 0)
        if code_item:
            code = code_item.text()
            self.stock_selected.emit(code)

    def save_watchlist(self):
        """保存自选股到本地"""
        try:
            settings = QSettings("AStockTrader", "Watchlist")
            settings.setValue("stocks", json.dumps(self.watchlist))
        except Exception as e:
            print(f"保存自选股失败: {e}")

    def load_watchlist(self):
        """从本地加载自选股"""
        try:
            settings = QSettings("AStockTrader", "Watchlist")
            data = settings.value("stocks", "[]")
            self.watchlist = json.loads(data)

            # 如果没有自选股，添加一些默认的
            if not self.watchlist:
                default_stocks = [
                    {'code': '000001', 'name': '平安银行'},
                    {'code': '600036', 'name': '招商银行'},
                    {'code': '600519', 'name': '贵州茅台'},
                    {'code': '000858', 'name': '五粮液'},
                    {'code': '002415', 'name': '海康威视'}
                ]
                self.watchlist = default_stocks
                self.save_watchlist()

            self.update_table()

        except Exception as e:
            print(f"加载自选股失败: {e}")
            self.watchlist = []

class StockChartWidget(QWidget):
    """股票图表组件"""

    def __init__(self):
        super().__init__()
        self.current_symbol = None
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(5, 5, 5, 5)

        # 图表标题和控制
        header_layout = QHBoxLayout()

        self.title_label = QLabel("请选择股票")
        self.title_label.setStyleSheet("color: #ffffff; font-size: 16px; font-weight: bold;")

        # 时间周期选择
        period_layout = QHBoxLayout()
        period_label = QLabel("周期:")
        period_label.setStyleSheet("color: #ffffff; font-size: 12px;")

        self.period_combo = QComboBox()
        self.period_combo.addItems(['1分钟', '5分钟', '15分钟', '30分钟', '1小时', '日线', '周线', '月线'])
        self.period_combo.setCurrentText('日线')
        self.period_combo.setStyleSheet("""
            QComboBox {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #404040;
                padding: 4px;
                border-radius: 4px;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #ffffff;
            }
        """)

        period_layout.addWidget(period_label)
        period_layout.addWidget(self.period_combo)
        period_layout.addStretch()

        header_layout.addWidget(self.title_label)
        header_layout.addStretch()
        header_layout.addLayout(period_layout)

        # 图表区域
        if CHARTS_AVAILABLE:
            self.chart_widget = pg.PlotWidget()
            self.chart_widget.setBackground('#1e1e1e')
            self.chart_widget.setLabel('left', '价格', color='white')
            self.chart_widget.setLabel('bottom', '时间', color='white')
            self.chart_widget.showGrid(x=True, y=True, alpha=0.3)
        else:
            self.chart_widget = QLabel("图表功能需要安装 pyqtgraph\n请运行: pip install pyqtgraph")
            self.chart_widget.setAlignment(Qt.AlignCenter)
            self.chart_widget.setStyleSheet("color: #ffffff; font-size: 14px;")

        layout.addLayout(header_layout)
        layout.addWidget(self.chart_widget)

        self.setLayout(layout)

    def update_chart(self, symbol, stock_data):
        """更新图表"""
        if not CHARTS_AVAILABLE:
            return

        self.current_symbol = symbol
        self.title_label.setText(f"{stock_data['name']} ({symbol})")

        # 生成模拟K线数据
        np.random.seed(hash(symbol) % 2**32)

        # 生成30天的数据
        dates = pd.date_range(end=datetime.now(), periods=30, freq='D')
        base_price = stock_data['price']

        prices = []
        current_price = base_price * 0.95  # 从较低价格开始

        for _ in range(30):
            change = np.random.normal(0, 0.02)
            current_price = current_price * (1 + change)
            prices.append(current_price)

        # 最后一个价格设为当前价格
        prices[-1] = stock_data['price']

        # 清除旧数据
        self.chart_widget.clear()

        # 绘制价格线
        x = list(range(len(prices)))
        self.chart_widget.plot(x, prices, pen=pg.mkPen(color='#00ff00', width=2), name='价格')

        # 添加移动平均线
        if len(prices) >= 5:
            ma5 = TechnicalAnalyzer.calculate_ma(prices, 5)
            ma5_clean = [p for p in ma5 if not np.isnan(p)]
            if ma5_clean:
                ma5_x = list(range(4, len(prices)))  # MA5从第5个点开始
                self.chart_widget.plot(ma5_x, ma5_clean, pen=pg.mkPen(color='#ffff00', width=1), name='MA5')

        if len(prices) >= 10:
            ma10 = TechnicalAnalyzer.calculate_ma(prices, 10)
            ma10_clean = [p for p in ma10 if not np.isnan(p)]
            if ma10_clean:
                ma10_x = list(range(9, len(prices)))  # MA10从第10个点开始
                self.chart_widget.plot(ma10_x, ma10_clean, pen=pg.mkPen(color='#ff00ff', width=1), name='MA10')

class SmartScreenerWidget(QWidget):
    """智能选股组件"""

    def __init__(self, data_provider):
        super().__init__()
        self.data_provider = data_provider
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)

        # 标题
        title_label = QLabel("智能选股")
        title_label.setStyleSheet("color: #ffffff; font-size: 18px; font-weight: bold;")

        # 筛选条件
        conditions_group = QGroupBox("筛选条件")
        conditions_group.setStyleSheet("""
            QGroupBox {
                color: #ffffff;
                font-size: 14px;
                font-weight: bold;
                border: 2px solid #404040;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)

        conditions_layout = QGridLayout()

        # 涨跌幅筛选
        conditions_layout.addWidget(QLabel("涨跌幅:"), 0, 0)
        self.change_combo = QComboBox()
        self.change_combo.addItems(['不限', '涨幅>5%', '涨幅>3%', '涨幅>1%', '跌幅>1%', '跌幅>3%', '跌幅>5%'])
        self.change_combo.setStyleSheet(self.get_combo_style())
        conditions_layout.addWidget(self.change_combo, 0, 1)

        # 市值筛选
        conditions_layout.addWidget(QLabel("市值:"), 0, 2)
        self.market_cap_combo = QComboBox()
        self.market_cap_combo.addItems(['不限', '大盘股(>500亿)', '中盘股(100-500亿)', '小盘股(<100亿)'])
        self.market_cap_combo.setStyleSheet(self.get_combo_style())
        conditions_layout.addWidget(self.market_cap_combo, 0, 3)

        # 成交量筛选
        conditions_layout.addWidget(QLabel("成交量:"), 1, 0)
        self.volume_combo = QComboBox()
        self.volume_combo.addItems(['不限', '放量(>2倍)', '温和放量(1.5-2倍)', '缩量(<0.5倍)'])
        self.volume_combo.setStyleSheet(self.get_combo_style())
        conditions_layout.addWidget(self.volume_combo, 1, 1)

        # 技术指标筛选
        conditions_layout.addWidget(QLabel("技术指标:"), 1, 2)
        self.technical_combo = QComboBox()
        self.technical_combo.addItems(['不限', 'MACD金叉', 'RSI超卖(<30)', 'RSI超买(>70)', 'KDJ金叉'])
        self.technical_combo.setStyleSheet(self.get_combo_style())
        conditions_layout.addWidget(self.technical_combo, 1, 3)

        conditions_group.setLayout(conditions_layout)

        # 投资策略
        strategy_group = QGroupBox("投资策略")
        strategy_group.setStyleSheet(conditions_group.styleSheet())

        strategy_layout = QHBoxLayout()

        self.strategy_combo = QComboBox()
        self.strategy_combo.addItems(['自定义', '动量策略', '价值投资', '成长股', '反转策略'])
        self.strategy_combo.setStyleSheet(self.get_combo_style())
        self.strategy_combo.currentTextChanged.connect(self.on_strategy_changed)

        strategy_layout.addWidget(QLabel("选择策略:"))
        strategy_layout.addWidget(self.strategy_combo)
        strategy_layout.addStretch()

        strategy_group.setLayout(strategy_layout)

        # 操作按钮
        button_layout = QHBoxLayout()

        self.screen_button = QPushButton("开始筛选")
        self.screen_button.setStyleSheet("""
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:pressed {
                background-color: #005a9e;
            }
        """)
        self.screen_button.clicked.connect(self.start_screening)

        reset_button = QPushButton("重置条件")
        reset_button.setStyleSheet("""
            QPushButton {
                background-color: #6b7280;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #4b5563;
            }
        """)
        reset_button.clicked.connect(self.reset_conditions)

        button_layout.addWidget(self.screen_button)
        button_layout.addWidget(reset_button)
        button_layout.addStretch()

        # 结果表格
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(7)
        self.results_table.setHorizontalHeaderLabels(['代码', '名称', '现价', '涨跌%', '成交量', '市值', '技术评分'])

        self.results_table.setStyleSheet("""
            QTableWidget {
                background-color: #1e1e1e;
                color: #ffffff;
                gridline-color: #404040;
                border: 1px solid #404040;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #404040;
            }
            QTableWidget::item:selected {
                background-color: #0078d4;
            }
            QHeaderView::section {
                background-color: #2b2b2b;
                color: #ffffff;
                padding: 8px;
                border: 1px solid #404040;
                font-weight: bold;
            }
        """)

        self.results_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.results_table.setAlternatingRowColors(True)
        self.results_table.horizontalHeader().setStretchLastSection(True)
        self.results_table.verticalHeader().setVisible(False)

        # 布局
        layout.addWidget(title_label)
        layout.addWidget(conditions_group)
        layout.addWidget(strategy_group)
        layout.addLayout(button_layout)
        layout.addWidget(QLabel("筛选结果:"))
        layout.addWidget(self.results_table)

        self.setLayout(layout)

    def get_combo_style(self):
        """获取下拉框样式"""
        return """
            QComboBox {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #404040;
                padding: 6px;
                border-radius: 4px;
                min-width: 120px;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #ffffff;
            }
            QComboBox QAbstractItemView {
                background-color: #2b2b2b;
                color: #ffffff;
                selection-background-color: #0078d4;
            }
        """

    def on_strategy_changed(self, strategy):
        """策略改变事件"""
        if strategy == '动量策略':
            self.change_combo.setCurrentText('涨幅>3%')
            self.volume_combo.setCurrentText('放量(>2倍)')
            self.technical_combo.setCurrentText('MACD金叉')
        elif strategy == '价值投资':
            self.market_cap_combo.setCurrentText('大盘股(>500亿)')
            self.change_combo.setCurrentText('不限')
        elif strategy == '成长股':
            self.market_cap_combo.setCurrentText('中盘股(100-500亿)')
            self.change_combo.setCurrentText('涨幅>1%')
        elif strategy == '反转策略':
            self.change_combo.setCurrentText('跌幅>3%')
            self.technical_combo.setCurrentText('RSI超卖(<30)')

    def reset_conditions(self):
        """重置筛选条件"""
        self.change_combo.setCurrentText('不限')
        self.market_cap_combo.setCurrentText('不限')
        self.volume_combo.setCurrentText('不限')
        self.technical_combo.setCurrentText('不限')
        self.strategy_combo.setCurrentText('自定义')
        self.results_table.setRowCount(0)

    def start_screening(self):
        """开始筛选"""
        self.screen_button.setText("筛选中...")
        self.screen_button.setEnabled(False)

        # 模拟筛选过程
        QTimer.singleShot(2000, self.finish_screening)

    def finish_screening(self):
        """完成筛选"""
        # 生成模拟筛选结果
        results = self.generate_mock_results()

        # 更新结果表格
        self.update_results_table(results)

        self.screen_button.setText("开始筛选")
        self.screen_button.setEnabled(True)

    def generate_mock_results(self):
        """生成模拟筛选结果"""
        stock_codes = ['000001', '000002', '600036', '600519', '000858', '002415', '000063', '600887', '002304', '300059']
        stock_names = ['平安银行', '万科A', '招商银行', '贵州茅台', '五粮液', '海康威视', '中兴通讯', '伊利股份', '洋河股份', '东方财富']

        results = []
        for i, (code, name) in enumerate(zip(stock_codes, stock_names)):
            np.random.seed(hash(code) % 2**32)

            result = {
                'code': code,
                'name': name,
                'price': round(10 + np.random.uniform(5, 100), 2),
                'change_pct': round(np.random.uniform(-8, 8), 2),
                'volume': int(np.random.randint(1000000, 100000000)),
                'market_cap': round(np.random.uniform(50, 5000), 2),
                'technical_score': round(np.random.uniform(30, 95), 1)
            }
            results.append(result)

        # 根据当前筛选条件过滤
        filtered_results = []
        for result in results:
            if self.matches_conditions(result):
                filtered_results.append(result)

        return filtered_results

    def matches_conditions(self, result):
        """检查是否符合筛选条件"""
        # 涨跌幅筛选
        change_filter = self.change_combo.currentText()
        if change_filter != '不限':
            change_pct = result['change_pct']
            if '涨幅>5%' in change_filter and change_pct <= 5:
                return False
            elif '涨幅>3%' in change_filter and change_pct <= 3:
                return False
            elif '涨幅>1%' in change_filter and change_pct <= 1:
                return False
            elif '跌幅>1%' in change_filter and change_pct >= -1:
                return False
            elif '跌幅>3%' in change_filter and change_pct >= -3:
                return False
            elif '跌幅>5%' in change_filter and change_pct >= -5:
                return False

        # 市值筛选
        market_cap_filter = self.market_cap_combo.currentText()
        if market_cap_filter != '不限':
            market_cap = result['market_cap']
            if '大盘股' in market_cap_filter and market_cap < 500:
                return False
            elif '中盘股' in market_cap_filter and (market_cap < 100 or market_cap > 500):
                return False
            elif '小盘股' in market_cap_filter and market_cap > 100:
                return False

        return True

    def update_results_table(self, results):
        """更新结果表格"""
        self.results_table.setRowCount(len(results))

        for i, result in enumerate(results):
            # 代码
            self.results_table.setItem(i, 0, QTableWidgetItem(result['code']))

            # 名称
            self.results_table.setItem(i, 1, QTableWidgetItem(result['name']))

            # 现价
            price_item = QTableWidgetItem(f"{result['price']:.2f}")
            self.results_table.setItem(i, 2, price_item)

            # 涨跌%
            change_pct = result['change_pct']
            change_item = QTableWidgetItem(f"{change_pct:+.2f}%")
            if change_pct > 0:
                change_item.setForeground(QColor("#ff4444"))  # 红涨
            elif change_pct < 0:
                change_item.setForeground(QColor("#00ff00"))  # 绿跌
            self.results_table.setItem(i, 3, change_item)

            # 成交量
            volume = result['volume']
            if volume > 100000000:
                volume_text = f"{volume/100000000:.1f}亿"
            elif volume > 10000:
                volume_text = f"{volume/10000:.1f}万"
            else:
                volume_text = str(volume)
            self.results_table.setItem(i, 4, QTableWidgetItem(volume_text))

            # 市值
            market_cap = result['market_cap']
            if market_cap > 1000:
                market_cap_text = f"{market_cap/1000:.1f}千亿"
            else:
                market_cap_text = f"{market_cap:.1f}亿"
            self.results_table.setItem(i, 5, QTableWidgetItem(market_cap_text))

            # 技术评分
            score = result['technical_score']
            score_item = QTableWidgetItem(f"{score:.1f}")
            if score >= 80:
                score_item.setForeground(QColor("#ff4444"))  # 高分红色
            elif score >= 60:
                score_item.setForeground(QColor("#ffff00"))  # 中分黄色
            else:
                score_item.setForeground(QColor("#00ff00"))  # 低分绿色
            self.results_table.setItem(i, 6, score_item)

class MainWindow(QMainWindow):
    """主窗口"""

    def __init__(self):
        super().__init__()
        self.data_provider = StockDataProvider()
        self.data_thread = None
        self.init_ui()
        self.setup_data_updates()
        self.setup_shortcuts()

    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("A股智能交易分析平台 - 专业版")
        self.setGeometry(100, 100, 1600, 1000)

        # 设置应用图标
        self.setWindowIcon(QIcon())

        # 设置深色主题
        self.setStyleSheet("""
            QMainWindow {
                background-color: #1e1e1e;
                color: #ffffff;
            }
            QWidget {
                background-color: #1e1e1e;
                color: #ffffff;
            }
            QLabel {
                color: #ffffff;
            }
        """)

        # 创建菜单栏
        self.create_menu_bar()

        # 创建状态栏
        self.create_status_bar()

        # 创建中央部件
        self.create_central_widget()

        # 创建工具栏（需要在central_widget之后，因为需要引用stock_list）
        self.create_toolbar()

        # 设置窗口居中
        self.center_window()

    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        menubar.setStyleSheet("""
            QMenuBar {
                background-color: #2b2b2b;
                color: #ffffff;
                border-bottom: 1px solid #404040;
            }
            QMenuBar::item {
                background-color: transparent;
                padding: 8px 12px;
            }
            QMenuBar::item:selected {
                background-color: #0078d4;
            }
            QMenu {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #404040;
            }
            QMenu::item {
                padding: 8px 20px;
            }
            QMenu::item:selected {
                background-color: #0078d4;
            }
        """)

        # 文件菜单
        file_menu = menubar.addMenu('文件')

        import_action = QAction('导入自选股', self)
        import_action.setShortcut('Ctrl+I')
        import_action.triggered.connect(self.import_watchlist)
        file_menu.addAction(import_action)

        export_action = QAction('导出自选股', self)
        export_action.setShortcut('Ctrl+E')
        export_action.triggered.connect(self.export_watchlist)
        file_menu.addAction(export_action)

        file_menu.addSeparator()

        exit_action = QAction('退出', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 视图菜单
        view_menu = menubar.addMenu('视图')

        fullscreen_action = QAction('全屏', self)
        fullscreen_action.setShortcut('F11')
        fullscreen_action.triggered.connect(self.toggle_fullscreen)
        view_menu.addAction(fullscreen_action)

        # 工具菜单
        tools_menu = menubar.addMenu('工具')

        screener_action = QAction('智能选股', self)
        screener_action.setShortcut('Ctrl+S')
        screener_action.triggered.connect(lambda: self.tab_widget.setCurrentIndex(1))
        tools_menu.addAction(screener_action)

        # 帮助菜单
        help_menu = menubar.addMenu('帮助')

        about_action = QAction('关于', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def create_toolbar(self):
        """创建工具栏"""
        toolbar = self.addToolBar('主工具栏')
        toolbar.setStyleSheet("""
            QToolBar {
                background-color: #2b2b2b;
                border: none;
                spacing: 5px;
                padding: 5px;
            }
            QToolButton {
                background-color: #404040;
                color: #ffffff;
                border: none;
                padding: 8px 12px;
                border-radius: 4px;
                font-size: 12px;
            }
            QToolButton:hover {
                background-color: #0078d4;
            }
            QToolButton:pressed {
                background-color: #005a9e;
            }
        """)

        # 刷新按钮
        refresh_action = QAction('刷新数据', self)
        refresh_action.setShortcut('F5')
        refresh_action.triggered.connect(self.refresh_data)
        toolbar.addAction(refresh_action)

        toolbar.addSeparator()

        # 添加股票按钮
        add_stock_action = QAction('添加股票', self)
        add_stock_action.setShortcut('Ctrl+A')
        add_stock_action.triggered.connect(self.stock_list.add_stock_dialog)
        toolbar.addAction(add_stock_action)

        toolbar.addSeparator()

        # 搜索框
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("搜索股票代码或名称...")
        self.search_edit.setStyleSheet("""
            QLineEdit {
                background-color: #404040;
                color: #ffffff;
                border: 1px solid #606060;
                padding: 6px 10px;
                border-radius: 4px;
                font-size: 12px;
                min-width: 200px;
            }
            QLineEdit:focus {
                border-color: #0078d4;
            }
        """)
        self.search_edit.returnPressed.connect(self.search_stock)
        toolbar.addWidget(self.search_edit)

        search_action = QAction('搜索', self)
        search_action.triggered.connect(self.search_stock)
        toolbar.addAction(search_action)

    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = self.statusBar()
        self.status_bar.setStyleSheet("""
            QStatusBar {
                background-color: #2b2b2b;
                color: #ffffff;
                border-top: 1px solid #404040;
            }
        """)

        # 连接状态
        self.connection_label = QLabel("数据连接: 正常")
        self.connection_label.setStyleSheet("color: #00ff00;")
        self.status_bar.addWidget(self.connection_label)

        # 更新时间
        self.update_time_label = QLabel("最后更新: --")
        self.status_bar.addPermanentWidget(self.update_time_label)

        # 股票数量
        self.stock_count_label = QLabel("自选股: 0")
        self.status_bar.addPermanentWidget(self.stock_count_label)

    def create_central_widget(self):
        """创建中央部件"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(5)

        # 市场概览
        self.market_overview = MarketOverviewWidget()
        main_layout.addWidget(self.market_overview)

        # 创建标签页
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #404040;
                background-color: #1e1e1e;
            }
            QTabBar::tab {
                background-color: #2b2b2b;
                color: #ffffff;
                padding: 10px 20px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background-color: #0078d4;
            }
            QTabBar::tab:hover {
                background-color: #404040;
            }
        """)

        # 主界面标签页
        main_tab = QWidget()
        main_tab_layout = QHBoxLayout()
        main_tab_layout.setContentsMargins(5, 5, 5, 5)

        # 左侧：自选股列表
        self.stock_list = StockListWidget()
        self.stock_list.stock_selected.connect(self.on_stock_selected)

        # 右侧：图表
        self.stock_chart = StockChartWidget()

        # 使用分割器
        splitter = QSplitter(Qt.Horizontal)
        splitter.addWidget(self.stock_list)
        splitter.addWidget(self.stock_chart)
        splitter.setSizes([400, 800])  # 设置初始比例
        splitter.setStyleSheet("""
            QSplitter::handle {
                background-color: #404040;
                width: 3px;
            }
            QSplitter::handle:hover {
                background-color: #0078d4;
            }
        """)

        main_tab_layout.addWidget(splitter)
        main_tab.setLayout(main_tab_layout)

        # 智能选股标签页
        self.smart_screener = SmartScreenerWidget(self.data_provider)

        # 添加标签页
        self.tab_widget.addTab(main_tab, "主界面")
        self.tab_widget.addTab(self.smart_screener, "智能选股")

        main_layout.addWidget(self.tab_widget)
        central_widget.setLayout(main_layout)

    def setup_data_updates(self):
        """设置数据更新"""
        # 获取自选股代码列表
        watchlist_symbols = [stock['code'] for stock in self.stock_list.watchlist]

        # 启动数据更新线程
        self.data_thread = StockDataThread(watchlist_symbols, self.data_provider)
        self.data_thread.data_updated.connect(self.on_data_updated)
        self.data_thread.start()

    def setup_shortcuts(self):
        """设置快捷键"""
        # ESC键退出全屏
        escape_shortcut = QShortcut(QKeySequence("Escape"), self)
        escape_shortcut.activated.connect(self.exit_fullscreen)

        # Ctrl+1/2 切换标签页
        tab1_shortcut = QShortcut(QKeySequence("Ctrl+1"), self)
        tab1_shortcut.activated.connect(lambda: self.tab_widget.setCurrentIndex(0))

        tab2_shortcut = QShortcut(QKeySequence("Ctrl+2"), self)
        tab2_shortcut.activated.connect(lambda: self.tab_widget.setCurrentIndex(1))

    def center_window(self):
        """窗口居中"""
        screen = QApplication.primaryScreen().geometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)

    def on_data_updated(self, data):
        """数据更新回调"""
        try:
            # 更新市场指数
            if 'indices' in data:
                self.market_overview.update_indices(data['indices'])

            # 更新自选股数据
            if 'stocks' in data:
                self.stock_list.update_stocks_data(data['stocks'])

            # 更新状态栏
            current_time = datetime.now().strftime("%H:%M:%S")
            self.update_time_label.setText(f"最后更新: {current_time}")
            self.stock_count_label.setText(f"自选股: {len(self.stock_list.watchlist)}")

        except Exception as e:
            print(f"数据更新回调错误: {e}")

    def on_stock_selected(self, symbol):
        """股票选择回调"""
        stock_data = self.data_provider.get_stock_info(symbol)
        if stock_data:
            self.stock_chart.update_chart(symbol, stock_data)

    def search_stock(self):
        """搜索股票"""
        keyword = self.search_edit.text().strip()
        if not keyword:
            return

        results = self.data_provider.search_stocks(keyword)
        if results:
            # 显示搜索结果对话框
            self.show_search_results(results)
        else:
            QMessageBox.information(self, "搜索结果", "未找到相关股票")

    def show_search_results(self, results):
        """显示搜索结果"""
        dialog = QDialog(self)
        dialog.setWindowTitle("搜索结果")
        dialog.setModal(True)
        dialog.resize(400, 300)

        layout = QVBoxLayout()

        # 结果列表
        list_widget = QListWidget()
        list_widget.setStyleSheet("""
            QListWidget {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #404040;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #404040;
            }
            QListWidget::item:selected {
                background-color: #0078d4;
            }
        """)

        for result in results:
            item_text = f"{result['code']} - {result['name']}"
            list_widget.addItem(item_text)

        # 双击添加到自选
        def on_item_double_clicked(item):
            text = item.text()
            code = text.split(' - ')[0]
            name = text.split(' - ')[1]
            self.stock_list.add_stock(code, name)
            dialog.accept()

        list_widget.itemDoubleClicked.connect(on_item_double_clicked)

        layout.addWidget(QLabel("双击添加到自选股:"))
        layout.addWidget(list_widget)

        # 按钮
        button_layout = QHBoxLayout()
        close_button = QPushButton("关闭")
        close_button.clicked.connect(dialog.reject)
        button_layout.addStretch()
        button_layout.addWidget(close_button)

        layout.addLayout(button_layout)
        dialog.setLayout(layout)
        dialog.exec_()

    def refresh_data(self):
        """刷新数据"""
        if self.data_thread:
            # 重启数据线程
            self.data_thread.stop()
            self.setup_data_updates()

        self.connection_label.setText("数据连接: 刷新中...")
        self.connection_label.setStyleSheet("color: #ffff00;")

        QTimer.singleShot(2000, lambda: (
            self.connection_label.setText("数据连接: 正常"),
            self.connection_label.setStyleSheet("color: #00ff00;")
        ))

    def toggle_fullscreen(self):
        """切换全屏"""
        if self.isFullScreen():
            self.showNormal()
        else:
            self.showFullScreen()

    def exit_fullscreen(self):
        """退出全屏"""
        if self.isFullScreen():
            self.showNormal()

    def import_watchlist(self):
        """导入自选股"""
        QMessageBox.information(self, "功能提示", "导入功能开发中...")

    def export_watchlist(self):
        """导出自选股"""
        QMessageBox.information(self, "功能提示", "导出功能开发中...")

    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(self, "关于",
            "A股智能交易分析平台 - 专业版\n\n"
            "版本: 1.0.0\n"
            "专为交易员设计的专业级股票分析工具\n\n"
            "功能特色:\n"
            "• 实时股票数据\n"
            "• 智能选股系统\n"
            "• 专业技术分析\n"
            "• 自选股管理\n"
            "• 快捷键操作\n\n"
            "⚠️ 投资有风险，入市需谨慎")

    def closeEvent(self, event):
        """关闭事件"""
        if self.data_thread:
            self.data_thread.stop()
        event.accept()

def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用信息
    app.setApplicationName("A股智能交易分析平台")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("AStockTrader")

    # 设置应用图标
    app.setWindowIcon(QIcon())

    # 创建主窗口
    window = MainWindow()
    window.show()

    # 运行应用
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()