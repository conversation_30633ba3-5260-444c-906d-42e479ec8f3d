#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专业交易组件
包含Level-2行情、快速下单、仓位管理等专业功能
"""

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
import time
from datetime import datetime
import json

class Level2QuoteWidget(QWidget):
    """Level-2五档行情组件"""
    
    def __init__(self):
        super().__init__()
        self.current_symbol = None
        self.init_ui()
        
    def init_ui(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(2)
        
        # 标题
        title_layout = QHBoxLayout()
        self.title_label = QLabel("五档行情")
        self.title_label.setStyleSheet("color: #ffffff; font-size: 14px; font-weight: bold;")
        
        self.symbol_label = QLabel("--")
        self.symbol_label.setStyleSheet("color: #00ff00; font-size: 12px;")
        
        title_layout.addWidget(self.title_label)
        title_layout.addStretch()
        title_layout.addWidget(self.symbol_label)
        
        # 五档表格
        self.quote_table = QTableWidget(10, 3)
        self.quote_table.setHorizontalHeaderLabels(['档位', '价格', '数量'])
        self.quote_table.setStyleSheet("""
            QTableWidget {
                background-color: #1a1a1a;
                color: #ffffff;
                gridline-color: #333333;
                border: 1px solid #333333;
                font-family: 'Monaco', 'Consolas', monospace;
                font-size: 11px;
            }
            QTableWidget::item {
                padding: 4px;
                border-bottom: 1px solid #333333;
            }
            QHeaderView::section {
                background-color: #2a2a2a;
                color: #ffffff;
                padding: 6px;
                border: 1px solid #333333;
                font-weight: bold;
                font-size: 10px;
            }
        """)
        
        # 设置列宽
        self.quote_table.setColumnWidth(0, 40)
        self.quote_table.setColumnWidth(1, 60)
        self.quote_table.setColumnWidth(2, 80)
        
        # 禁用编辑
        self.quote_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.quote_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.quote_table.verticalHeader().setVisible(False)
        
        # 初始化表格
        self.init_quote_table()
        
        layout.addLayout(title_layout)
        layout.addWidget(self.quote_table)
        self.setLayout(layout)
        
    def init_quote_table(self):
        """初始化五档表格"""
        # 卖盘（倒序显示）
        for i in range(5):
            row = 4 - i  # 卖5到卖1
            self.quote_table.setItem(row, 0, QTableWidgetItem(f"卖{5-i}"))
            self.quote_table.setItem(row, 1, QTableWidgetItem("--"))
            self.quote_table.setItem(row, 2, QTableWidgetItem("--"))
            
            # 设置卖盘颜色（绿色）
            for col in range(3):
                item = self.quote_table.item(row, col)
                if item:
                    item.setForeground(QColor("#00aa00"))
        
        # 买盘
        for i in range(5):
            row = 5 + i  # 买1到买5
            self.quote_table.setItem(row, 0, QTableWidgetItem(f"买{i+1}"))
            self.quote_table.setItem(row, 1, QTableWidgetItem("--"))
            self.quote_table.setItem(row, 2, QTableWidgetItem("--"))
            
            # 设置买盘颜色（红色）
            for col in range(3):
                item = self.quote_table.item(row, col)
                if item:
                    item.setForeground(QColor("#ff4444"))
    
    def update_level2_data(self, symbol, level2_data):
        """更新Level-2数据"""
        if not level2_data:
            return
            
        self.current_symbol = symbol
        self.symbol_label.setText(symbol)
        
        # 更新卖盘（ask）
        asks = level2_data.get('asks', [])
        for i, ask in enumerate(asks[:5]):
            row = 4 - i  # 卖5到卖1
            price = ask.get('price', 0)
            volume = ask.get('volume', 0)
            
            if price > 0:
                self.quote_table.setItem(row, 1, QTableWidgetItem(f"{price:.2f}"))
                self.quote_table.setItem(row, 2, QTableWidgetItem(f"{volume:,}"))
        
        # 更新买盘（bid）
        bids = level2_data.get('bids', [])
        for i, bid in enumerate(bids[:5]):
            row = 5 + i  # 买1到买5
            price = bid.get('price', 0)
            volume = bid.get('volume', 0)
            
            if price > 0:
                self.quote_table.setItem(row, 1, QTableWidgetItem(f"{price:.2f}"))
                self.quote_table.setItem(row, 2, QTableWidgetItem(f"{volume:,}"))

class QuickOrderWidget(QWidget):
    """快速下单组件"""
    
    order_signal = pyqtSignal(dict)  # 下单信号
    
    def __init__(self):
        super().__init__()
        self.current_symbol = None
        self.current_price = 0
        self.init_ui()
        
    def init_ui(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(8)
        
        # 标题
        title_label = QLabel("快速下单")
        title_label.setStyleSheet("color: #ffffff; font-size: 14px; font-weight: bold;")
        
        # 股票信息
        stock_layout = QHBoxLayout()
        self.stock_label = QLabel("股票: --")
        self.stock_label.setStyleSheet("color: #ffffff; font-size: 12px;")
        
        self.price_label = QLabel("现价: --")
        self.price_label.setStyleSheet("color: #00ff00; font-size: 12px; font-weight: bold;")
        
        stock_layout.addWidget(self.stock_label)
        stock_layout.addStretch()
        stock_layout.addWidget(self.price_label)
        
        # 下单表单
        form_layout = QFormLayout()
        form_layout.setSpacing(6)
        
        # 交易方向
        self.direction_combo = QComboBox()
        self.direction_combo.addItems(['买入', '卖出'])
        self.direction_combo.setStyleSheet(self.get_combo_style())
        
        # 价格类型
        self.price_type_combo = QComboBox()
        self.price_type_combo.addItems(['限价', '市价', '五档即成', '即成剩撤'])
        self.price_type_combo.setStyleSheet(self.get_combo_style())
        self.price_type_combo.currentTextChanged.connect(self.on_price_type_changed)
        
        # 委托价格
        self.price_edit = QDoubleSpinBox()
        self.price_edit.setRange(0.01, 9999.99)
        self.price_edit.setDecimals(2)
        self.price_edit.setSingleStep(0.01)
        self.price_edit.setStyleSheet(self.get_spinbox_style())
        
        # 委托数量
        self.quantity_edit = QSpinBox()
        self.quantity_edit.setRange(100, 999999900)
        self.quantity_edit.setSingleStep(100)
        self.quantity_edit.setSuffix(" 股")
        self.quantity_edit.setStyleSheet(self.get_spinbox_style())
        
        form_layout.addRow("方向:", self.direction_combo)
        form_layout.addRow("类型:", self.price_type_combo)
        form_layout.addRow("价格:", self.price_edit)
        form_layout.addRow("数量:", self.quantity_edit)
        
        # 快速数量按钮
        quantity_layout = QHBoxLayout()
        quantities = [100, 500, 1000, 2000, 5000]
        for qty in quantities:
            btn = QPushButton(f"{qty}")
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #404040;
                    color: #ffffff;
                    border: 1px solid #606060;
                    padding: 4px 8px;
                    border-radius: 3px;
                    font-size: 10px;
                }
                QPushButton:hover {
                    background-color: #505050;
                }
                QPushButton:pressed {
                    background-color: #303030;
                }
            """)
            btn.clicked.connect(lambda checked, q=qty: self.quantity_edit.setValue(q))
            quantity_layout.addWidget(btn)
        
        # 预计金额
        self.amount_label = QLabel("预计金额: ¥0.00")
        self.amount_label.setStyleSheet("color: #ffff00; font-size: 11px;")
        
        # 连接信号
        self.price_edit.valueChanged.connect(self.update_amount)
        self.quantity_edit.valueChanged.connect(self.update_amount)
        
        # 下单按钮
        button_layout = QHBoxLayout()
        
        self.buy_button = QPushButton("买入")
        self.buy_button.setStyleSheet("""
            QPushButton {
                background-color: #ff4444;
                color: #ffffff;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #ff6666;
            }
            QPushButton:pressed {
                background-color: #cc3333;
            }
        """)
        self.buy_button.clicked.connect(lambda: self.place_order('buy'))
        
        self.sell_button = QPushButton("卖出")
        self.sell_button.setStyleSheet("""
            QPushButton {
                background-color: #00aa00;
                color: #ffffff;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #00cc00;
            }
            QPushButton:pressed {
                background-color: #008800;
            }
        """)
        self.sell_button.clicked.connect(lambda: self.place_order('sell'))
        
        button_layout.addWidget(self.buy_button)
        button_layout.addWidget(self.sell_button)
        
        # 布局
        layout.addWidget(title_label)
        layout.addLayout(stock_layout)
        layout.addLayout(form_layout)
        layout.addLayout(quantity_layout)
        layout.addWidget(self.amount_label)
        layout.addLayout(button_layout)
        layout.addStretch()
        
        self.setLayout(layout)
    
    def get_combo_style(self):
        """获取下拉框样式"""
        return """
            QComboBox {
                background-color: #2a2a2a;
                color: #ffffff;
                border: 1px solid #404040;
                padding: 4px 8px;
                border-radius: 3px;
                font-size: 11px;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 4px solid #ffffff;
            }
            QComboBox QAbstractItemView {
                background-color: #2a2a2a;
                color: #ffffff;
                selection-background-color: #0078d4;
            }
        """
    
    def get_spinbox_style(self):
        """获取数字输入框样式"""
        return """
            QSpinBox, QDoubleSpinBox {
                background-color: #2a2a2a;
                color: #ffffff;
                border: 1px solid #404040;
                padding: 4px 8px;
                border-radius: 3px;
                font-size: 11px;
            }
            QSpinBox::up-button, QDoubleSpinBox::up-button {
                background-color: #404040;
                border: none;
                width: 16px;
            }
            QSpinBox::down-button, QDoubleSpinBox::down-button {
                background-color: #404040;
                border: none;
                width: 16px;
            }
        """
    
    def on_price_type_changed(self, price_type):
        """价格类型改变"""
        if price_type in ['市价', '五档即成', '即成剩撤']:
            self.price_edit.setEnabled(False)
            self.price_edit.setValue(self.current_price)
        else:
            self.price_edit.setEnabled(True)
    
    def update_stock_info(self, symbol, stock_data):
        """更新股票信息"""
        if not stock_data:
            return
            
        self.current_symbol = symbol
        self.current_price = stock_data.get('price', 0)
        
        name = stock_data.get('name', '')
        self.stock_label.setText(f"股票: {symbol} {name}")
        self.price_label.setText(f"现价: ¥{self.current_price:.2f}")
        
        # 更新委托价格
        if self.price_edit.isEnabled():
            self.price_edit.setValue(self.current_price)
        
        self.update_amount()
    
    def update_amount(self):
        """更新预计金额"""
        price = self.price_edit.value()
        quantity = self.quantity_edit.value()
        amount = price * quantity
        self.amount_label.setText(f"预计金额: ¥{amount:,.2f}")
    
    def place_order(self, direction):
        """下单"""
        if not self.current_symbol:
            QMessageBox.warning(self, "警告", "请先选择股票")
            return
        
        order_data = {
            'symbol': self.current_symbol,
            'direction': direction,
            'price_type': self.price_type_combo.currentText(),
            'price': self.price_edit.value(),
            'quantity': self.quantity_edit.value(),
            'amount': self.price_edit.value() * self.quantity_edit.value(),
            'timestamp': time.time()
        }
        
        # 确认对话框
        direction_text = "买入" if direction == 'buy' else "卖出"
        msg = f"确认{direction_text}？\n\n"
        msg += f"股票: {self.current_symbol}\n"
        msg += f"价格: ¥{order_data['price']:.2f}\n"
        msg += f"数量: {order_data['quantity']:,}股\n"
        msg += f"金额: ¥{order_data['amount']:,.2f}"
        
        reply = QMessageBox.question(self, "确认下单", msg, 
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            # 发送下单信号
            self.order_signal.emit(order_data)
            
            # 模拟下单结果
            QMessageBox.information(self, "下单成功", 
                                  f"{direction_text}委托已提交\n"
                                  f"委托编号: {int(time.time())}")

class PositionWidget(QWidget):
    """持仓管理组件"""
    
    def __init__(self):
        super().__init__()
        self.positions = []
        self.init_ui()
        
    def init_ui(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 标题和统计
        header_layout = QHBoxLayout()
        
        title_label = QLabel("持仓管理")
        title_label.setStyleSheet("color: #ffffff; font-size: 14px; font-weight: bold;")
        
        self.total_value_label = QLabel("总市值: ¥0.00")
        self.total_value_label.setStyleSheet("color: #00ff00; font-size: 12px; font-weight: bold;")
        
        self.total_pnl_label = QLabel("总盈亏: ¥0.00")
        self.total_pnl_label.setStyleSheet("color: #ffffff; font-size: 12px;")
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addWidget(self.total_value_label)
        header_layout.addWidget(self.total_pnl_label)
        
        # 持仓表格
        self.position_table = QTableWidget()
        self.position_table.setColumnCount(8)
        self.position_table.setHorizontalHeaderLabels([
            '股票代码', '股票名称', '持仓数量', '成本价', '现价', '市值', '盈亏', '盈亏率'
        ])
        
        self.position_table.setStyleSheet("""
            QTableWidget {
                background-color: #1a1a1a;
                color: #ffffff;
                gridline-color: #333333;
                border: 1px solid #333333;
                font-family: 'Monaco', 'Consolas', monospace;
                font-size: 11px;
            }
            QTableWidget::item {
                padding: 6px;
                border-bottom: 1px solid #333333;
            }
            QTableWidget::item:selected {
                background-color: #0078d4;
            }
            QHeaderView::section {
                background-color: #2a2a2a;
                color: #ffffff;
                padding: 8px;
                border: 1px solid #333333;
                font-weight: bold;
                font-size: 10px;
            }
        """)
        
        # 设置表格属性
        self.position_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.position_table.setAlternatingRowColors(True)
        self.position_table.horizontalHeader().setStretchLastSection(True)
        self.position_table.verticalHeader().setVisible(False)
        
        # 右键菜单
        self.position_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.position_table.customContextMenuRequested.connect(self.show_context_menu)
        
        layout.addLayout(header_layout)
        layout.addWidget(self.position_table)
        
        self.setLayout(layout)
        
        # 加载模拟持仓数据
        self.load_mock_positions()
    
    def load_mock_positions(self):
        """加载模拟持仓数据"""
        mock_positions = [
            {
                'symbol': '000001',
                'name': '平安银行',
                'quantity': 1000,
                'cost_price': 12.30,
                'current_price': 12.50
            },
            {
                'symbol': '600036',
                'name': '招商银行',
                'quantity': 500,
                'cost_price': 45.20,
                'current_price': 45.60
            }
        ]
        
        self.update_positions(mock_positions)
    
    def update_positions(self, positions):
        """更新持仓数据"""
        self.positions = positions
        self.position_table.setRowCount(len(positions))
        
        total_value = 0
        total_pnl = 0
        
        for i, pos in enumerate(positions):
            symbol = pos['symbol']
            name = pos['name']
            quantity = pos['quantity']
            cost_price = pos['cost_price']
            current_price = pos['current_price']
            
            market_value = quantity * current_price
            cost_value = quantity * cost_price
            pnl = market_value - cost_value
            pnl_rate = (pnl / cost_value * 100) if cost_value > 0 else 0
            
            total_value += market_value
            total_pnl += pnl
            
            # 填充表格
            self.position_table.setItem(i, 0, QTableWidgetItem(symbol))
            self.position_table.setItem(i, 1, QTableWidgetItem(name))
            self.position_table.setItem(i, 2, QTableWidgetItem(f"{quantity:,}"))
            self.position_table.setItem(i, 3, QTableWidgetItem(f"{cost_price:.2f}"))
            self.position_table.setItem(i, 4, QTableWidgetItem(f"{current_price:.2f}"))
            self.position_table.setItem(i, 5, QTableWidgetItem(f"{market_value:,.2f}"))
            
            # 盈亏显示
            pnl_item = QTableWidgetItem(f"{pnl:+,.2f}")
            pnl_rate_item = QTableWidgetItem(f"{pnl_rate:+.2f}%")
            
            # 设置盈亏颜色
            color = QColor("#ff4444") if pnl > 0 else QColor("#00aa00") if pnl < 0 else QColor("#ffffff")
            pnl_item.setForeground(color)
            pnl_rate_item.setForeground(color)
            
            self.position_table.setItem(i, 6, pnl_item)
            self.position_table.setItem(i, 7, pnl_rate_item)
        
        # 更新统计信息
        self.total_value_label.setText(f"总市值: ¥{total_value:,.2f}")
        
        pnl_color = "#ff4444" if total_pnl > 0 else "#00aa00" if total_pnl < 0 else "#ffffff"
        self.total_pnl_label.setText(f"总盈亏: ¥{total_pnl:+,.2f}")
        self.total_pnl_label.setStyleSheet(f"color: {pnl_color}; font-size: 12px; font-weight: bold;")
    
    def show_context_menu(self, position):
        """显示右键菜单"""
        menu = QMenu(self)
        menu.setStyleSheet("""
            QMenu {
                background-color: #2a2a2a;
                color: #ffffff;
                border: 1px solid #404040;
            }
            QMenu::item {
                padding: 8px 16px;
            }
            QMenu::item:selected {
                background-color: #0078d4;
            }
        """)
        
        sell_action = menu.addAction("卖出")
        add_action = menu.addAction("加仓")
        stop_loss_action = menu.addAction("设置止损")
        
        action = menu.exec_(self.position_table.mapToGlobal(position))
        
        if action == sell_action:
            self.sell_position()
        elif action == add_action:
            self.add_position()
        elif action == stop_loss_action:
            self.set_stop_loss()
    
    def sell_position(self):
        """卖出持仓"""
        QMessageBox.information(self, "提示", "卖出功能开发中...")
    
    def add_position(self):
        """加仓"""
        QMessageBox.information(self, "提示", "加仓功能开发中...")
    
    def set_stop_loss(self):
        """设置止损"""
        QMessageBox.information(self, "提示", "止损功能开发中...")

class ProfessionalChartWidget(QWidget):
    """专业K线图表组件"""

    def __init__(self):
        super().__init__()
        self.current_symbol = None
        self.kline_data = []
        self.indicators = {}
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(2, 2, 2, 2)
        layout.setSpacing(2)

        # 图表控制栏
        control_layout = QHBoxLayout()
        control_layout.setSpacing(5)

        # 股票信息
        self.stock_info_label = QLabel("请选择股票")
        self.stock_info_label.setStyleSheet("color: #ffffff; font-size: 12px; font-weight: bold;")

        # 周期选择
        period_label = QLabel("周期:")
        period_label.setStyleSheet("color: #ffffff; font-size: 11px;")

        self.period_combo = QComboBox()
        self.period_combo.addItems(['1分', '5分', '15分', '30分', '60分', '日线', '周线', '月线'])
        self.period_combo.setCurrentText('日线')
        self.period_combo.setStyleSheet(self.get_combo_style())
        self.period_combo.currentTextChanged.connect(self.on_period_changed)

        # 技术指标选择
        indicator_label = QLabel("指标:")
        indicator_label.setStyleSheet("color: #ffffff; font-size: 11px;")

        self.ma_checkbox = QCheckBox("均线")
        self.ma_checkbox.setChecked(True)
        self.ma_checkbox.setStyleSheet("color: #ffffff; font-size: 11px;")

        self.volume_checkbox = QCheckBox("成交量")
        self.volume_checkbox.setChecked(True)
        self.volume_checkbox.setStyleSheet("color: #ffffff; font-size: 11px;")

        self.macd_checkbox = QCheckBox("MACD")
        self.macd_checkbox.setStyleSheet("color: #ffffff; font-size: 11px;")

        self.rsi_checkbox = QCheckBox("RSI")
        self.rsi_checkbox.setStyleSheet("color: #ffffff; font-size: 11px;")

        # 全屏按钮
        fullscreen_btn = QPushButton("全屏")
        fullscreen_btn.setStyleSheet("""
            QPushButton {
                background-color: #404040;
                color: #ffffff;
                border: 1px solid #606060;
                padding: 4px 8px;
                border-radius: 3px;
                font-size: 10px;
            }
            QPushButton:hover {
                background-color: #505050;
            }
        """)

        control_layout.addWidget(self.stock_info_label)
        control_layout.addStretch()
        control_layout.addWidget(period_label)
        control_layout.addWidget(self.period_combo)
        control_layout.addWidget(indicator_label)
        control_layout.addWidget(self.ma_checkbox)
        control_layout.addWidget(self.volume_checkbox)
        control_layout.addWidget(self.macd_checkbox)
        control_layout.addWidget(self.rsi_checkbox)
        control_layout.addWidget(fullscreen_btn)

        # 图表区域
        try:
            import pyqtgraph as pg

            # 主图表（K线+均线）
            self.main_chart = pg.PlotWidget()
            self.main_chart.setBackground('#1a1a1a')
            self.main_chart.setLabel('left', '价格', color='white', size='10pt')
            self.main_chart.showGrid(x=True, y=True, alpha=0.3)
            self.main_chart.setMinimumHeight(300)

            # 成交量图表
            self.volume_chart = pg.PlotWidget()
            self.volume_chart.setBackground('#1a1a1a')
            self.volume_chart.setLabel('left', '成交量', color='white', size='10pt')
            self.volume_chart.showGrid(x=True, y=True, alpha=0.3)
            self.volume_chart.setMaximumHeight(100)

            # MACD图表
            self.macd_chart = pg.PlotWidget()
            self.macd_chart.setBackground('#1a1a1a')
            self.macd_chart.setLabel('left', 'MACD', color='white', size='10pt')
            self.macd_chart.showGrid(x=True, y=True, alpha=0.3)
            self.macd_chart.setMaximumHeight(80)
            self.macd_chart.hide()

            # RSI图表
            self.rsi_chart = pg.PlotWidget()
            self.rsi_chart.setBackground('#1a1a1a')
            self.rsi_chart.setLabel('left', 'RSI', color='white', size='10pt')
            self.rsi_chart.showGrid(x=True, y=True, alpha=0.3)
            self.rsi_chart.setMaximumHeight(80)
            self.rsi_chart.hide()

            # 连接指标复选框
            self.volume_checkbox.toggled.connect(self.toggle_volume_chart)
            self.macd_checkbox.toggled.connect(self.toggle_macd_chart)
            self.rsi_checkbox.toggled.connect(self.toggle_rsi_chart)

            layout.addLayout(control_layout)
            layout.addWidget(self.main_chart, 3)  # 主图占3份
            layout.addWidget(self.volume_chart, 1)  # 成交量占1份
            layout.addWidget(self.macd_chart, 1)
            layout.addWidget(self.rsi_chart, 1)

        except ImportError:
            # 如果没有pyqtgraph，显示提示
            chart_placeholder = QLabel("专业图表功能需要安装 pyqtgraph\n请运行: pip install pyqtgraph")
            chart_placeholder.setAlignment(Qt.AlignCenter)
            chart_placeholder.setStyleSheet("color: #ffffff; font-size: 14px;")
            layout.addLayout(control_layout)
            layout.addWidget(chart_placeholder)

        self.setLayout(layout)

    def get_combo_style(self):
        """获取下拉框样式"""
        return """
            QComboBox {
                background-color: #2a2a2a;
                color: #ffffff;
                border: 1px solid #404040;
                padding: 3px 6px;
                border-radius: 3px;
                font-size: 10px;
                min-width: 50px;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 4px solid #ffffff;
            }
            QComboBox QAbstractItemView {
                background-color: #2a2a2a;
                color: #ffffff;
                selection-background-color: #0078d4;
            }
        """

    def on_period_changed(self, period):
        """周期改变"""
        if self.current_symbol:
            self.update_chart(self.current_symbol, None)  # 重新加载数据

    def toggle_volume_chart(self, checked):
        """切换成交量图表显示"""
        if checked:
            self.volume_chart.show()
        else:
            self.volume_chart.hide()

    def toggle_macd_chart(self, checked):
        """切换MACD图表显示"""
        if checked:
            self.macd_chart.show()
        else:
            self.macd_chart.hide()

    def toggle_rsi_chart(self, checked):
        """切换RSI图表显示"""
        if checked:
            self.rsi_chart.show()
        else:
            self.rsi_chart.hide()

    def update_chart(self, symbol, stock_data):
        """更新图表"""
        try:
            import pyqtgraph as pg

            self.current_symbol = symbol

            if stock_data:
                name = stock_data.get('name', '')
                price = stock_data.get('price', 0)
                change = stock_data.get('change', 0)
                change_pct = stock_data.get('change_pct', 0)

                # 更新股票信息
                color = "#ff4444" if change > 0 else "#00aa00" if change < 0 else "#ffffff"
                self.stock_info_label.setText(
                    f"{symbol} {name} ¥{price:.2f} "
                    f"<span style='color: {color}'>{change:+.2f} ({change_pct:+.2f}%)</span>"
                )

            # 生成模拟K线数据
            self.generate_kline_data(symbol)

            # 绘制K线图
            self.draw_kline()

            # 绘制均线
            if self.ma_checkbox.isChecked():
                self.draw_moving_averages()

            # 绘制成交量
            if self.volume_checkbox.isChecked():
                self.draw_volume()

            # 绘制MACD
            if self.macd_checkbox.isChecked():
                self.draw_macd()

            # 绘制RSI
            if self.rsi_checkbox.isChecked():
                self.draw_rsi()

        except ImportError:
            pass

    def generate_kline_data(self, symbol):
        """生成模拟K线数据"""
        import numpy as np

        # 根据周期确定数据点数
        period = self.period_combo.currentText()
        if '分' in period:
            data_points = 240  # 4小时数据
        elif period == '日线':
            data_points = 60   # 60天数据
        elif period == '周线':
            data_points = 52   # 52周数据
        else:
            data_points = 60

        # 生成模拟数据
        np.random.seed(hash(symbol) % 2**32)

        base_price = 10 + (hash(symbol) % 100)
        prices = []
        volumes = []
        current_price = base_price

        for i in range(data_points):
            # 价格波动
            change = np.random.normal(0, 0.02)
            current_price = current_price * (1 + change)

            # OHLC数据
            open_price = current_price * (1 + np.random.normal(0, 0.005))
            high_price = max(open_price, current_price) * (1 + abs(np.random.normal(0, 0.01)))
            low_price = min(open_price, current_price) * (1 - abs(np.random.normal(0, 0.01)))
            close_price = current_price

            prices.append({
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price
            })

            # 成交量
            volume = np.random.randint(1000000, 10000000)
            volumes.append(volume)

        self.kline_data = prices
        self.volume_data = volumes

    def draw_kline(self):
        """绘制K线图"""
        try:
            import pyqtgraph as pg

            self.main_chart.clear()

            if not self.kline_data:
                return

            # 绘制K线
            x = list(range(len(self.kline_data)))

            # 收盘价线
            close_prices = [k['close'] for k in self.kline_data]
            self.main_chart.plot(x, close_prices, pen=pg.mkPen(color='#ffffff', width=1))

        except ImportError:
            pass

    def draw_moving_averages(self):
        """绘制移动平均线"""
        try:
            import pyqtgraph as pg

            if len(self.kline_data) < 20:
                return

            close_prices = [k['close'] for k in self.kline_data]
            x = list(range(len(close_prices)))

            # MA5
            ma5 = self.calculate_ma(close_prices, 5)
            if len(ma5) > 0:
                ma5_x = list(range(4, len(close_prices)))
                self.main_chart.plot(ma5_x, ma5, pen=pg.mkPen(color='#ffff00', width=1), name='MA5')

            # MA10
            ma10 = self.calculate_ma(close_prices, 10)
            if len(ma10) > 0:
                ma10_x = list(range(9, len(close_prices)))
                self.main_chart.plot(ma10_x, ma10, pen=pg.mkPen(color='#ff00ff', width=1), name='MA10')

            # MA20
            ma20 = self.calculate_ma(close_prices, 20)
            if len(ma20) > 0:
                ma20_x = list(range(19, len(close_prices)))
                self.main_chart.plot(ma20_x, ma20, pen=pg.mkPen(color='#00ffff', width=1), name='MA20')

        except ImportError:
            pass

    def draw_volume(self):
        """绘制成交量"""
        try:
            import pyqtgraph as pg

            self.volume_chart.clear()

            if not self.volume_data:
                return

            x = list(range(len(self.volume_data)))

            # 成交量柱状图
            bargraph = pg.BarGraphItem(x=x, height=self.volume_data, width=0.8,
                                     brush=pg.mkBrush(color='#404040'))
            self.volume_chart.addItem(bargraph)

        except ImportError:
            pass

    def draw_macd(self):
        """绘制MACD"""
        try:
            import pyqtgraph as pg

            self.macd_chart.clear()

            if len(self.kline_data) < 26:
                return

            close_prices = [k['close'] for k in self.kline_data]
            macd_data = self.calculate_macd(close_prices)

            if macd_data:
                x = list(range(len(macd_data['macd'])))

                # MACD线
                self.macd_chart.plot(x, macd_data['macd'], pen=pg.mkPen(color='#ffffff', width=1))

                # 信号线
                self.macd_chart.plot(x, macd_data['signal'], pen=pg.mkPen(color='#ffff00', width=1))

                # 柱状图
                histogram = [macd_data['macd'][i] - macd_data['signal'][i] for i in range(len(x))]
                bargraph = pg.BarGraphItem(x=x, height=histogram, width=0.8,
                                         brush=pg.mkBrush(color='#ff4444'))
                self.macd_chart.addItem(bargraph)

        except ImportError:
            pass

    def draw_rsi(self):
        """绘制RSI"""
        try:
            import pyqtgraph as pg

            self.rsi_chart.clear()

            if len(self.kline_data) < 14:
                return

            close_prices = [k['close'] for k in self.kline_data]
            rsi_data = self.calculate_rsi(close_prices)

            if rsi_data:
                x = list(range(len(rsi_data)))

                # RSI线
                self.rsi_chart.plot(x, rsi_data, pen=pg.mkPen(color='#00ff00', width=1))

                # 超买超卖线
                overbought = [70] * len(x)
                oversold = [30] * len(x)
                self.rsi_chart.plot(x, overbought, pen=pg.mkPen(color='#ff4444', width=1, style=Qt.DashLine))
                self.rsi_chart.plot(x, oversold, pen=pg.mkPen(color='#ff4444', width=1, style=Qt.DashLine))

        except ImportError:
            pass

    def calculate_ma(self, prices, period):
        """计算移动平均线"""
        if len(prices) < period:
            return []

        ma = []
        for i in range(period - 1, len(prices)):
            ma.append(sum(prices[i-period+1:i+1]) / period)

        return ma

    def calculate_macd(self, prices, fast=12, slow=26, signal=9):
        """计算MACD"""
        if len(prices) < slow:
            return None

        import pandas as pd

        prices_series = pd.Series(prices)
        ema_fast = prices_series.ewm(span=fast).mean()
        ema_slow = prices_series.ewm(span=slow).mean()

        macd = ema_fast - ema_slow
        signal_line = macd.ewm(span=signal).mean()

        return {
            'macd': macd.fillna(0).tolist(),
            'signal': signal_line.fillna(0).tolist()
        }

    def calculate_rsi(self, prices, period=14):
        """计算RSI"""
        if len(prices) < period + 1:
            return None

        import pandas as pd
        import numpy as np

        deltas = np.diff(prices)
        gains = pd.Series(np.where(deltas > 0, deltas, 0))
        losses = pd.Series(np.where(deltas < 0, -deltas, 0))

        avg_gains = gains.rolling(window=period).mean()
        avg_losses = losses.rolling(window=period).mean()

        rs = avg_gains / (avg_losses + 1e-10)
        rsi = 100 - (100 / (1 + rs))

        return rsi.fillna(50).tolist()
