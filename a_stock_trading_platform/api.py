#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A股智能交易分析平台 - 后端API服务
提供股票数据获取、智能筛选、技术分析等功能
"""

from flask import Flask, jsonify, request, send_from_directory
from flask_cors import CORS
import pandas as pd
import numpy as np
import yfinance as yf
import akshare as ak
import talib
from datetime import datetime, timedelta
import json
import os

app = Flask(__name__)
CORS(app)

class StockAnalyzer:
    def __init__(self):
        self.stock_data_cache = {}
        self.cache_timeout = 300  # 5分钟缓存
        
    def get_stock_data(self, symbol, period='1d'):
        """获取股票数据"""
        cache_key = f"{symbol}_{period}"
        current_time = datetime.now()
        
        # 检查缓存
        if cache_key in self.stock_data_cache:
            cached_data, cached_time = self.stock_data_cache[cache_key]
            if (current_time - cached_time).seconds < self.cache_timeout:
                return cached_data
        
        try:
            # 使用akshare获取A股数据
            if symbol.startswith('00') or symbol.startswith('30'):
                # 深圳股票
                df = ak.stock_zh_a_hist(symbol=symbol, period="daily", start_date="20240101", end_date=datetime.now().strftime("%Y%m%d"), adjust="")
            elif symbol.startswith('60') or symbol.startswith('68'):
                # 上海股票
                df = ak.stock_zh_a_hist(symbol=symbol, period="daily", start_date="20240101", end_date=datetime.now().strftime("%Y%m%d"), adjust="")
            else:
                return None
                
            # 缓存数据
            self.stock_data_cache[cache_key] = (df, current_time)
            return df
            
        except Exception as e:
            print(f"获取股票数据失败: {e}")
            return None
    
    def calculate_technical_indicators(self, df):
        """计算技术指标"""
        if df is None or df.empty:
            return {}
            
        try:
            close = df['收盘'].values
            high = df['最高'].values
            low = df['最低'].values
            volume = df['成交量'].values
            
            # 移动平均线
            ma5 = talib.SMA(close, timeperiod=5)
            ma10 = talib.SMA(close, timeperiod=10)
            ma20 = talib.SMA(close, timeperiod=20)
            ma60 = talib.SMA(close, timeperiod=60)
            
            # MACD
            macd, macdsignal, macdhist = talib.MACD(close, fastperiod=12, slowperiod=26, signalperiod=9)
            
            # RSI
            rsi = talib.RSI(close, timeperiod=14)
            
            # KDJ
            k, d = talib.STOCH(high, low, close, fastk_period=9, slowk_period=3, slowd_period=3)
            j = 3 * k - 2 * d
            
            # 布林带
            upper, middle, lower = talib.BBANDS(close, timeperiod=20, nbdevup=2, nbdevdn=2, matype=0)
            
            # 成交量指标
            volume_ma5 = talib.SMA(volume.astype(float), timeperiod=5)
            
            return {
                'ma5': ma5[-1] if len(ma5) > 0 and not np.isnan(ma5[-1]) else None,
                'ma10': ma10[-1] if len(ma10) > 0 and not np.isnan(ma10[-1]) else None,
                'ma20': ma20[-1] if len(ma20) > 0 and not np.isnan(ma20[-1]) else None,
                'ma60': ma60[-1] if len(ma60) > 0 and not np.isnan(ma60[-1]) else None,
                'macd': macd[-1] if len(macd) > 0 and not np.isnan(macd[-1]) else None,
                'macd_signal': macdsignal[-1] if len(macdsignal) > 0 and not np.isnan(macdsignal[-1]) else None,
                'macd_hist': macdhist[-1] if len(macdhist) > 0 and not np.isnan(macdhist[-1]) else None,
                'rsi': rsi[-1] if len(rsi) > 0 and not np.isnan(rsi[-1]) else None,
                'k': k[-1] if len(k) > 0 and not np.isnan(k[-1]) else None,
                'd': d[-1] if len(d) > 0 and not np.isnan(d[-1]) else None,
                'j': j[-1] if len(j) > 0 and not np.isnan(j[-1]) else None,
                'bb_upper': upper[-1] if len(upper) > 0 and not np.isnan(upper[-1]) else None,
                'bb_middle': middle[-1] if len(middle) > 0 and not np.isnan(middle[-1]) else None,
                'bb_lower': lower[-1] if len(lower) > 0 and not np.isnan(lower[-1]) else None,
                'volume_ratio': volume[-1] / volume_ma5[-1] if len(volume_ma5) > 0 and not np.isnan(volume_ma5[-1]) and volume_ma5[-1] != 0 else None
            }
            
        except Exception as e:
            print(f"计算技术指标失败: {e}")
            return {}
    
    def calculate_technical_score(self, indicators, current_price):
        """计算技术评分"""
        score = 50  # 基础分数
        
        try:
            # MA趋势评分
            if indicators.get('ma5') and indicators.get('ma10') and indicators.get('ma20'):
                if current_price > indicators['ma5'] > indicators['ma10'] > indicators['ma20']:
                    score += 20  # 多头排列
                elif current_price < indicators['ma5'] < indicators['ma10'] < indicators['ma20']:
                    score -= 20  # 空头排列
            
            # MACD评分
            if indicators.get('macd') and indicators.get('macd_signal'):
                if indicators['macd'] > indicators['macd_signal'] and indicators['macd'] > 0:
                    score += 15  # MACD金叉且在零轴上方
                elif indicators['macd'] < indicators['macd_signal'] and indicators['macd'] < 0:
                    score -= 15  # MACD死叉且在零轴下方
            
            # RSI评分
            if indicators.get('rsi'):
                if 30 < indicators['rsi'] < 70:
                    score += 10  # RSI在正常区间
                elif indicators['rsi'] > 80:
                    score -= 10  # 超买
                elif indicators['rsi'] < 20:
                    score += 5   # 超卖反弹机会
            
            # KDJ评分
            if indicators.get('k') and indicators.get('d'):
                if indicators['k'] > indicators['d'] and indicators['k'] < 80:
                    score += 10  # KDJ金叉且未超买
                elif indicators['k'] < indicators['d'] and indicators['k'] > 20:
                    score -= 10  # KDJ死叉且未超卖
            
            # 成交量评分
            if indicators.get('volume_ratio'):
                if 1.5 < indicators['volume_ratio'] < 3:
                    score += 10  # 适度放量
                elif indicators['volume_ratio'] > 5:
                    score -= 5   # 过度放量
            
            return max(0, min(100, score))
            
        except Exception as e:
            print(f"计算技术评分失败: {e}")
            return 50

analyzer = StockAnalyzer()

@app.route('/')
def index():
    """返回主页"""
    return send_from_directory('.', 'index.html')

@app.route('/<path:filename>')
def static_files(filename):
    """返回静态文件"""
    return send_from_directory('.', filename)

@app.route('/api/market/indices')
def get_market_indices():
    """获取市场指数"""
    try:
        # 获取主要指数
        indices = {}
        
        # 上证指数
        sh_data = ak.stock_zh_index_daily(symbol="sh000001")
        if not sh_data.empty:
            latest = sh_data.iloc[-1]
            prev = sh_data.iloc[-2] if len(sh_data) > 1 else latest
            indices['sh'] = {
                'name': '上证指数',
                'value': float(latest['close']),
                'change': float(latest['close'] - prev['close']),
                'change_pct': float((latest['close'] - prev['close']) / prev['close'] * 100)
            }
        
        # 深证成指
        sz_data = ak.stock_zh_index_daily(symbol="sz399001")
        if not sz_data.empty:
            latest = sz_data.iloc[-1]
            prev = sz_data.iloc[-2] if len(sz_data) > 1 else latest
            indices['sz'] = {
                'name': '深证成指',
                'value': float(latest['close']),
                'change': float(latest['close'] - prev['close']),
                'change_pct': float((latest['close'] - prev['close']) / prev['close'] * 100)
            }
        
        # 创业板指
        cy_data = ak.stock_zh_index_daily(symbol="sz399006")
        if not cy_data.empty:
            latest = cy_data.iloc[-1]
            prev = cy_data.iloc[-2] if len(cy_data) > 1 else latest
            indices['cy'] = {
                'name': '创业板指',
                'value': float(latest['close']),
                'change': float(latest['close'] - prev['close']),
                'change_pct': float((latest['close'] - prev['close']) / prev['close'] * 100)
            }
        
        return jsonify({'success': True, 'data': indices})
        
    except Exception as e:
        print(f"获取市场指数失败: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/stock/info/<symbol>')
def get_stock_info(symbol):
    """获取股票基本信息"""
    try:
        # 获取股票基本信息
        stock_info = ak.stock_individual_info_em(symbol=symbol)
        
        # 获取实时价格
        realtime_data = ak.stock_zh_a_spot_em()
        stock_realtime = realtime_data[realtime_data['代码'] == symbol]
        
        if stock_realtime.empty:
            return jsonify({'success': False, 'error': '股票不存在'})
        
        stock_data = stock_realtime.iloc[0]
        
        # 获取技术指标
        df = analyzer.get_stock_data(symbol)
        indicators = analyzer.calculate_technical_indicators(df)
        technical_score = analyzer.calculate_technical_score(indicators, float(stock_data['最新价']))
        
        result = {
            'code': symbol,
            'name': stock_data['名称'],
            'price': float(stock_data['最新价']),
            'change': float(stock_data['涨跌额']),
            'change_pct': float(stock_data['涨跌幅']),
            'volume': int(stock_data['成交量']),
            'turnover': float(stock_data['成交额']),
            'market_cap': float(stock_data['总市值']) if '总市值' in stock_data else None,
            'pe_ratio': float(stock_data['市盈率-动态']) if '市盈率-动态' in stock_data else None,
            'pb_ratio': float(stock_data['市净率']) if '市净率' in stock_data else None,
            'technical_indicators': indicators,
            'technical_score': technical_score
        }
        
        return jsonify({'success': True, 'data': result})
        
    except Exception as e:
        print(f"获取股票信息失败: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/stock/screen', methods=['POST'])
def screen_stocks():
    """智能股票筛选"""
    try:
        filters = request.json
        
        # 获取所有A股列表
        stock_list = ak.stock_zh_a_spot_em()
        
        # 应用筛选条件
        filtered_stocks = []
        
        for _, stock in stock_list.iterrows():
            try:
                # 基本筛选
                if filters.get('market_cap'):
                    market_cap = float(stock.get('总市值', 0))
                    if filters['market_cap'] == 'large' and market_cap < 50000000000:
                        continue
                    elif filters['market_cap'] == 'mid' and (market_cap < 10000000000 or market_cap > 50000000000):
                        continue
                    elif filters['market_cap'] == 'small' and market_cap > 10000000000:
                        continue
                
                if filters.get('change'):
                    change_pct = float(stock['涨跌幅'])
                    if filters['change'] == 'up5' and change_pct < 5:
                        continue
                    elif filters['change'] == 'up3' and change_pct < 3:
                        continue
                    elif filters['change'] == 'down3' and change_pct > -3:
                        continue
                    elif filters['change'] == 'down5' and change_pct > -5:
                        continue
                
                # 技术指标筛选
                if filters.get('technical'):
                    df = analyzer.get_stock_data(stock['代码'])
                    if df is not None and not df.empty:
                        indicators = analyzer.calculate_technical_indicators(df)
                        
                        if filters['technical'] == 'macd_golden':
                            if not (indicators.get('macd', 0) > indicators.get('macd_signal', 0)):
                                continue
                        elif filters['technical'] == 'kdj_oversold':
                            if not (indicators.get('k', 50) < 20):
                                continue
                        elif filters['technical'] == 'rsi_strong':
                            if not (indicators.get('rsi', 50) > 60):
                                continue
                
                # 计算技术评分
                df = analyzer.get_stock_data(stock['代码'])
                indicators = analyzer.calculate_technical_indicators(df)
                technical_score = analyzer.calculate_technical_score(indicators, float(stock['最新价']))
                
                filtered_stocks.append({
                    'code': stock['代码'],
                    'name': stock['名称'],
                    'price': float(stock['最新价']),
                    'change': float(stock['涨跌额']),
                    'change_pct': float(stock['涨跌幅']),
                    'volume': int(stock['成交量']),
                    'market_cap': float(stock.get('总市值', 0)),
                    'technical_score': technical_score
                })
                
                # 限制结果数量
                if len(filtered_stocks) >= 50:
                    break
                    
            except Exception as e:
                print(f"处理股票 {stock.get('代码', 'unknown')} 时出错: {e}")
                continue
        
        # 根据策略排序
        if filters.get('strategy'):
            if filters['strategy'] == 'momentum':
                filtered_stocks.sort(key=lambda x: x['change_pct'], reverse=True)
            elif filters['strategy'] == 'value':
                filtered_stocks.sort(key=lambda x: x['price'])
            elif filters['strategy'] == 'growth':
                filtered_stocks.sort(key=lambda x: x['technical_score'], reverse=True)
            elif filters['strategy'] == 'reversal':
                filtered_stocks.sort(key=lambda x: x['change_pct'])
        
        return jsonify({'success': True, 'data': filtered_stocks[:50]})
        
    except Exception as e:
        print(f"股票筛选失败: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/stock/hot')
def get_hot_stocks():
    """获取热门股票"""
    try:
        # 获取涨幅榜前10
        hot_stocks = ak.stock_zh_a_spot_em().nlargest(10, '涨跌幅')
        
        result = []
        for _, stock in hot_stocks.iterrows():
            result.append({
                'code': stock['代码'],
                'name': stock['名称'],
                'price': float(stock['最新价']),
                'change': float(stock['涨跌额']),
                'change_pct': float(stock['涨跌幅']),
                'volume': int(stock['成交量'])
            })
        
        return jsonify({'success': True, 'data': result})
        
    except Exception as e:
        print(f"获取热门股票失败: {e}")
        return jsonify({'success': False, 'error': str(e)})

if __name__ == '__main__':
    print("🚀 启动A股智能交易分析平台后端服务...")
    print("📊 支持功能:")
    print("   • 实时股票数据获取")
    print("   • 智能股票筛选")
    print("   • 技术指标计算")
    print("   • 市场指数监控")
    print("🌐 访问地址: http://localhost:5000")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
